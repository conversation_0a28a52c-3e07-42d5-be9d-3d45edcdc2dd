// Remove Cart Item API Route
// DELETE /api/e-commerce/cart/remove - Remove item from cart

import { NextRequest, NextResponse } from 'next/server'
import { cartService, handleEcommerceError } from '@/lib/ecommerce'
import { RemoveFromCartInput } from '@/lib/ecommerce/types'

export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json()
    const { cartItemId, cartId } = body

    if (!cartItemId || !cartId) {
      return NextResponse.json(
        { success: false, error: 'Cart ID and cart item ID are required' },
        { status: 400 }
      )
    }

    const removeInput: RemoveFromCartInput = {
      itemId: cartItemId
    }

    const result = await cartService().removeFromCart(cartId, removeInput)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Remove cart item API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
