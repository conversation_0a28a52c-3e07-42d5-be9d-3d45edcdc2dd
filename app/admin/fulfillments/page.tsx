'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Package, Truck, MapPin, Clock, CheckCircle, XCircle, Search, Filter, Plus, Eye, Edit, Trash2 } from 'lucide-react'
import { toast } from 'sonner'
import { FulfillmentDashboard } from '@/components/admin/fulfillment/fulfillment-dashboard'

interface Fulfillment {
  id: string
  orderId: string
  status: string
  carrier: string
  service: string
  trackingNumber?: string
  trackingUrl?: string
  locationName: string
  notes?: string
  createdAt: string
  shippedAt?: string
  deliveredAt?: string
  estimatedDeliveryAt?: string
  order: {
    orderNumber: string
    customerEmail: string
    customerFirstName?: string
    customerLastName?: string
    total: number
    currency: string
  }
  items: Array<{
    id: string
    quantity: number
    orderItem: {
      productTitle: string
      variantTitle?: string
      unitPrice: number
      product: {
        name: string
        sku: string
        images: string[]
      }
    }
  }>
}

interface FulfillmentMetrics {
  totalFulfillments: number
  totalItemsFulfilled: number
  averageFulfillmentTimeHours: number
  onTimeDeliveryRate: string
  fulfillmentRate: string
  statusBreakdown: Record<string, number>
  carrierBreakdown: Record<string, number>
}

export default function FulfillmentsPage() {
  const [fulfillments, setFulfillments] = useState<Fulfillment[]>([])
  const [metrics, setMetrics] = useState<FulfillmentMetrics | null>(null)
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [carrierFilter, setCarrierFilter] = useState('all')
  const [selectedFulfillment, setSelectedFulfillment] = useState<Fulfillment | null>(null)
  const [showCreateDialog, setShowCreateDialog] = useState(false)

  useEffect(() => {
    fetchFulfillments()
    fetchMetrics()
  }, [])

  const fetchFulfillments = async () => {
    try {
      const response = await fetch('/api/e-commerce/fulfillments', {
        headers: { 'x-admin-request': 'true' }
      })
      const data = await response.json()

      if (data.success) {
        // Handle both array and paginated response formats
        let fulfillmentsData = []

        if (Array.isArray(data.data)) {
          // Direct array response
          fulfillmentsData = data.data
        } else if (data.data && Array.isArray(data.data.data)) {
          // Paginated response with data.data structure
          fulfillmentsData = data.data.data
        } else if (data.data && data.data.fulfillments && Array.isArray(data.data.fulfillments)) {
          // Alternative structure
          fulfillmentsData = data.data.fulfillments
        }

        setFulfillments(fulfillmentsData)
      } else {
        toast.error(data.error || 'Failed to fetch fulfillments')
        setFulfillments([])
      }
    } catch (error) {
      console.error('Fetch fulfillments error:', error)
      toast.error('Failed to fetch fulfillments')
      setFulfillments([])
    } finally {
      setLoading(false)
    }
  }

  const fetchMetrics = async () => {
    try {
      const response = await fetch('/api/e-commerce/fulfillments/metrics?period=30d', {
        headers: { 'x-admin-request': 'true' }
      })
      const data = await response.json()
      
      if (data.success) {
        setMetrics(data.data.overview)
      }
    } catch (error) {
      console.error('Fetch metrics error:', error)
    }
  }

  const updateFulfillmentStatus = async (fulfillmentId: string, status: string, notes?: string) => {
    try {
      const response = await fetch(`/api/e-commerce/fulfillments/${fulfillmentId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-request': 'true'
        },
        body: JSON.stringify({ status, notes })
      })

      const data = await response.json()
      
      if (data.success) {
        toast.success(`Fulfillment status updated to ${status}`)
        fetchFulfillments()
      } else {
        toast.error(data.error || 'Failed to update fulfillment')
      }
    } catch (error) {
      console.error('Update fulfillment error:', error)
      toast.error('Failed to update fulfillment')
    }
  }

  const trackFulfillment = async (fulfillmentId: string) => {
    try {
      const response = await fetch(`/api/e-commerce/fulfillments/${fulfillmentId}/track`)
      const data = await response.json()
      
      if (data.success) {
        toast.success('Tracking information updated')
        fetchFulfillments()
      } else {
        toast.error(data.error || 'Failed to track fulfillment')
      }
    } catch (error) {
      console.error('Track fulfillment error:', error)
      toast.error('Failed to track fulfillment')
    }
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'pending': return 'secondary'
      case 'open': return 'default'
      case 'in_transit': return 'default'
      case 'delivered': return 'default'
      case 'cancelled': return 'destructive'
      default: return 'secondary'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatCurrency = (amount: number, currency: string = 'ZAR') => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: currency
    }).format(amount)
  }

  // Filter fulfillments - ensure fulfillments is an array
  const filteredFulfillments = Array.isArray(fulfillments) ? fulfillments.filter(fulfillment => {
    const matchesSearch = searchTerm === '' ||
      fulfillment.order?.orderNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      fulfillment.trackingNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      fulfillment.order?.customerEmail?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === 'all' || fulfillment.status === statusFilter
    const matchesCarrier = carrierFilter === 'all' || fulfillment.carrier === carrierFilter

    return matchesSearch && matchesStatus && matchesCarrier
  }) : []

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Fulfillments</h1>
          <p className="text-muted-foreground">
            Manage order fulfillments, shipping, and tracking
          </p>
        </div>
        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Create Fulfillment
        </Button>
      </div>

      {/* Tabs for Dashboard and Management */}
      <Tabs defaultValue="dashboard" className="space-y-6">
        <TabsList>
          <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
          <TabsTrigger value="management">Management</TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard">
          <FulfillmentDashboard />
        </TabsContent>

        <TabsContent value="management" className="space-y-6">

      {/* Metrics Cards */}
      {metrics && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Fulfillments</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.totalFulfillments}</div>
              <p className="text-xs text-muted-foreground">
                {metrics.totalItemsFulfilled} items fulfilled
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg. Fulfillment Time</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.averageFulfillmentTimeHours}h</div>
              <p className="text-xs text-muted-foreground">
                From order to ship
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">On-Time Delivery</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.onTimeDeliveryRate}%</div>
              <p className="text-xs text-muted-foreground">
                Delivered on time
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Fulfillment Rate</CardTitle>
              <Truck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.fulfillmentRate}%</div>
              <p className="text-xs text-muted-foreground">
                Successfully fulfilled
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by order number, tracking number, or customer email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="open">Open</SelectItem>
                <SelectItem value="in_transit">In Transit</SelectItem>
                <SelectItem value="delivered">Delivered</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
            <Select value={carrierFilter} onValueChange={setCarrierFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by carrier" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Carriers</SelectItem>
                <SelectItem value="courier-guy">The Courier Guy</SelectItem>
                <SelectItem value="fastway">Fastway</SelectItem>
                <SelectItem value="postnet">PostNet</SelectItem>
                <SelectItem value="aramex">Aramex</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Fulfillments Table */}
      <Card>
        <CardHeader>
          <CardTitle>Fulfillments ({filteredFulfillments.length})</CardTitle>
          <CardDescription>
            Manage and track order fulfillments
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Order</TableHead>
                <TableHead>Customer</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Carrier</TableHead>
                <TableHead>Tracking</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredFulfillments.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <div className="text-muted-foreground">
                      {loading ? 'Loading fulfillments...' : 'No fulfillments found'}
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                filteredFulfillments.map((fulfillment) => (
                  <TableRow key={fulfillment.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">#{fulfillment.order?.orderNumber || 'N/A'}</div>
                        <div className="text-sm text-muted-foreground">
                          {fulfillment.order?.total ? formatCurrency(fulfillment.order.total, fulfillment.order.currency) : 'N/A'}
                        </div>
                      </div>
                    </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">
                        {fulfillment.order?.customerFirstName || ''} {fulfillment.order?.customerLastName || ''}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {fulfillment.order?.customerEmail || 'N/A'}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getStatusBadgeVariant(fulfillment.status || 'pending')}>
                      {(fulfillment.status || 'pending').replace('_', ' ')}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{fulfillment.carrier || 'N/A'}</div>
                      <div className="text-sm text-muted-foreground">{fulfillment.service || 'N/A'}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    {fulfillment.trackingNumber ? (
                      <div>
                        <div className="font-medium">{fulfillment.trackingNumber}</div>
                        <Button
                          variant="link"
                          size="sm"
                          className="p-0 h-auto"
                          onClick={() => trackFulfillment(fulfillment.id)}
                        >
                          Update tracking
                        </Button>
                      </div>
                    ) : (
                      <span className="text-muted-foreground">No tracking</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {fulfillment.createdAt ? formatDate(fulfillment.createdAt) : 'N/A'}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setSelectedFulfillment(fulfillment)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Select
                        value={fulfillment.status || 'pending'}
                        onValueChange={(status) => updateFulfillmentStatus(fulfillment.id, status)}
                      >
                        <SelectTrigger className="w-[120px]">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="pending">Pending</SelectItem>
                          <SelectItem value="open">Open</SelectItem>
                          <SelectItem value="in_transit">In Transit</SelectItem>
                          <SelectItem value="delivered">Delivered</SelectItem>
                          <SelectItem value="cancelled">Cancelled</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </TableCell>
                </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
