"use client"

import { useState } from "react"
import { ProductCard } from "@/components/storefront/products/product-card"
import { ProductFilters } from "@/components/storefront/products/product-filters"
import { ProductSort } from "@/components/storefront/products/product-sort"
import { Skeleton } from "@/components/ui/skeleton"
import { useHeritageProducts } from "@/lib/ecommerce/hooks/use-collection-products"
import { transformToStorefrontProducts } from "@/lib/ecommerce/utils/product-transformers"
import { Heart } from "lucide-react"

export default function HeritageDayPage() {
  const [sortBy, setSortBy] = useState("featured")
  const [filters, setFilters] = useState({
    category: "",
    color: "",
    size: "",
  })

  const { products: ecommerceProducts, loading, error } = useHeritageProducts({
    sortBy,
    filters: {
      // Convert legacy filters to e-commerce format
      ...(filters.category && { categoryIds: [filters.category] }),
      // Additional filters can be added here
    },
    limit: 20
  })

  // Transform e-commerce products to storefront format
  const products = transformToStorefrontProducts(ecommerceProducts)

  const handleFilterChange = (newFilters: typeof filters) => {
    setFilters(newFilters)
  }

  return (
    <div className="container px-4 md:px-6 py-6 md:py-10">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-2 mb-4">
          <span className="text-2xl">🇿🇦</span>
          <h1 className="text-2xl font-normal">Heritage Day Collection</h1>
        </div>
        <p className="text-gray-600 text-sm mb-4">
          Celebrate our beautiful South African heritage with pride
        </p>
        <div className="bg-gradient-to-r from-green-50 to-yellow-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <Heart className="h-4 w-4 text-red-500" />
            <span className="font-medium text-sm">Heritage Day Special</span>
          </div>
          <p className="text-sm text-gray-700">
            24 September - Celebrating the diversity that makes South Africa beautiful. 
            Free shipping on all orders over R800 until 30 September.
          </p>
        </div>
      </div>

      <div className="flex flex-col lg:flex-row gap-8">
        {/* Filters Sidebar */}
        <div className="lg:w-64 flex-shrink-0">
          <ProductFilters onFilterChange={handleFilterChange} />
        </div>

        {/* Main Content */}
        <div className="flex-1">
          {/* Sort and Results Count */}
          <div className="flex items-center justify-between mb-6 pb-4 border-b border-gray-100">
            <p className="text-sm text-gray-600">
              {loading ? "Loading..." : `${products.length} heritage products`}
            </p>
            <ProductSort value={sortBy} onValueChange={setSortBy} />
          </div>

          {/* Products Grid */}
          {loading ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-8">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="space-y-3">
                  <Skeleton className="aspect-[3/4] w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              ))}
            </div>
          ) : error ? (
            <div className="text-center py-16">
              <span className="text-6xl mb-4 block text-red-400">🇿🇦</span>
              <h3 className="text-lg font-medium mb-2">Error loading heritage products</h3>
              <p className="text-gray-600">
                {error.message}
              </p>
            </div>
          ) : products.length > 0 ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-8">
              {products.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <span className="text-6xl mb-4 block">🇿🇦</span>
              <h3 className="text-lg font-medium mb-2">No heritage products found</h3>
              <p className="text-gray-600">
                Try adjusting your filters to see more products.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Heritage Information */}
      <div className="mt-16 bg-gray-50 rounded-lg p-8">
        <h2 className="text-xl font-medium mb-4">About Heritage Day</h2>
        <div className="grid md:grid-cols-2 gap-6 text-sm text-gray-700">
          <div>
            <p className="mb-3">
              Heritage Day on 24 September is a day when all South Africans are encouraged to celebrate 
              their cultural heritage and the diversity of their beliefs and traditions.
            </p>
            <p>
              At Coco Milk Kids, we celebrate this beautiful diversity by creating clothing that 
              represents the vibrant spirit of our rainbow nation.
            </p>
          </div>
          <div>
            <h3 className="font-medium mb-2">Our Heritage Promise</h3>
            <ul className="space-y-1">
              <li>• Proudly designed in South Africa</li>
              <li>• Supporting local communities</li>
              <li>• Celebrating our diverse cultures</li>
              <li>• Quality clothing for all South African children</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
