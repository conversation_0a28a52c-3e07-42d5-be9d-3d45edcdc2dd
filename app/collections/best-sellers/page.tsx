"use client"

import { useState } from "react"
import { ProductCard } from "@/components/storefront/products/product-card"
import { ProductFilters } from "@/components/storefront/products/product-filters"
import { ProductSort } from "@/components/storefront/products/product-sort"
import { Skeleton } from "@/components/ui/skeleton"
import { useBestSellers } from "@/lib/ecommerce/hooks/use-collection-products"
import { transformToStorefrontProducts } from "@/lib/ecommerce/utils/product-transformers"
import { TrendingUp } from "lucide-react"

export default function BestSellersPage() {
  const [sortBy, setSortBy] = useState("featured")
  const [filters, setFilters] = useState({
    category: "",
    color: "",
    size: "",
  })

  const { products: ecommerceProducts, loading, error } = useBestSellers({
    sortBy,
    filters: {
      // Convert legacy filters to e-commerce format
      ...(filters.category && { categoryIds: [filters.category] }),
      // Additional filters can be added here
    },
    limit: 20
  })

  // Transform e-commerce products to storefront format
  const products = transformToStorefrontProducts(ecommerceProducts)

  const handleFilterChange = (newFilters: typeof filters) => {
    setFilters(newFilters)
  }

  return (
    <div className="container px-4 md:px-6 py-6 md:py-10">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-2 mb-2">
          <TrendingUp className="h-5 w-5 text-[#012169]" />
          <h1 className="text-2xl md:text-3xl font-bold font-montserrat">Best Sellers</h1>
        </div>
        <p className="text-muted-foreground font-light">
          Our most popular children's clothing - loved by families everywhere
        </p>
      </div>

      <div className="flex flex-col lg:flex-row gap-8">
        {/* Filters Sidebar */}
        <div className="lg:w-64 flex-shrink-0">
          <ProductFilters onFilterChange={handleFilterChange} />
        </div>

        {/* Main Content */}
        <div className="flex-1">
          {/* Sort and Results Count */}
          <div className="flex items-center justify-between mb-6">
            <p className="text-sm text-muted-foreground font-light">
              {loading ? "Loading..." : `${products.length} best selling products`}
            </p>
            <ProductSort value={sortBy} onValueChange={setSortBy} />
          </div>

          {/* Products Grid */}
          {loading ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="space-y-3">
                  <Skeleton className="aspect-[3/4] w-full rounded-none" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              ))}
            </div>
          ) : error ? (
            <div className="text-center py-16">
              <TrendingUp className="h-16 w-16 text-red-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Error loading products</h3>
              <p className="text-muted-foreground">
                {error.message}
              </p>
            </div>
          ) : products.length > 0 ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6">
              {products.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <TrendingUp className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No products found</h3>
              <p className="text-muted-foreground">
                Try adjusting your filters to see more products.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
