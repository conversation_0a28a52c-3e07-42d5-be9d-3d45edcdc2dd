#!/usr/bin/env tsx

/**
 * Direct API test to check if the products API is working
 */

import { NextRequest } from 'next/server'

// Import the API route handler directly
async function testApiRoute() {
  console.log('🧪 Testing API Route Directly\n')

  try {
    // Import the route handler
    const { GET } = await import('../app/api/e-commerce/products/route')
    
    // Create a mock request
    const url = new URL('http://localhost:3090/api/e-commerce/products?page=1&limit=5')
    const request = new NextRequest(url)
    
    console.log('📡 Calling GET handler...')
    const response = await GET(request)
    
    console.log('📊 Response status:', response.status)
    
    const data = await response.json()
    console.log('📦 Response data:', JSON.stringify(data, null, 2))
    
    if (data.success) {
      console.log('✅ API route is working!')
      console.log(`📦 Found ${data.data?.data?.length || 0} products`)
      console.log(`📄 Total: ${data.data?.pagination?.total || 0}`)
    } else {
      console.log('❌ API route returned error:', data.error)
    }
    
  } catch (error) {
    console.error('❌ Error testing API route:', error)
  }
}

// Test database connection
async function testDatabase() {
  console.log('\n🗄️ Testing Database Connection...')
  
  try {
    const { PrismaClient } = await import('@prisma/client')
    const prisma = new PrismaClient()
    
    const count = await prisma.product.count()
    console.log(`📦 Products in database: ${count}`)
    
    if (count > 0) {
      const sample = await prisma.product.findFirst({
        select: {
          id: true,
          title: true,
          status: true,
          price: true
        }
      })
      console.log('📋 Sample product:', sample)
    }
    
    await prisma.$disconnect()
    
  } catch (error) {
    console.error('❌ Database error:', error)
  }
}

// Test product service
async function testProductService() {
  console.log('\n🔧 Testing Product Service...')
  
  try {
    const { productService } = await import('../lib/ecommerce')
    
    const result = await productService().searchProducts({
      page: 1,
      limit: 5
    })
    
    console.log('📊 Service result:', {
      success: result.success,
      dataLength: result.data?.data?.length,
      total: result.data?.pagination?.total,
      error: result.error
    })
    
  } catch (error) {
    console.error('❌ Service error:', error)
  }
}

async function runTests() {
  await testDatabase()
  await testProductService()
  await testApiRoute()
}

runTests().catch(console.error)
