#!/usr/bin/env tsx

/**
 * Test the frontend API calls to ensure they work correctly
 */

// Simulate the frontend fetch calls
async function testFrontendApiCalls() {
  console.log('🧪 Testing Frontend API Calls\n')

  try {
    // Test 1: Basic products fetch (simulating useProducts hook)
    console.log('1️⃣ Testing basic products fetch...')
    const { GET: productsGET } = await import('../app/api/e-commerce/products/route')
    
    const url1 = new URL('http://localhost:3090/api/e-commerce/products?page=1&limit=20&sortBy=createdAt&sortOrder=desc')
    const request1 = new Request(url1)
    const response1 = await productsGET(request1)
    const data1 = await response1.json()
    
    console.log('📊 Response format check:')
    console.log('  - success:', data1.success)
    console.log('  - data.data exists:', !!data1.data?.data)
    console.log('  - data.pagination exists:', !!data1.data?.pagination)
    console.log('  - products count:', data1.data?.data?.length || 0)
    console.log('  - total products:', data1.data?.pagination?.total || 0)
    
    if (data1.success && data1.data?.data && data1.data?.pagination) {
      console.log('✅ Basic products fetch working correctly')
    } else {
      console.log('❌ Basic products fetch format issue')
      return false
    }

    // Test 2: Filtered products fetch
    console.log('\n2️⃣ Testing filtered products fetch...')
    const url2 = new URL('http://localhost:3090/api/e-commerce/products?page=1&limit=10&status=active')
    const request2 = new Request(url2)
    const response2 = await productsGET(request2)
    const data2 = await response2.json()
    
    console.log('📊 Filtered results:')
    console.log('  - products count:', data2.data?.data?.length || 0)
    console.log('  - all active status:', data2.data?.data?.every((p: any) => p.status === 'active') || false)
    
    if (data2.success && data2.data?.data) {
      console.log('✅ Filtered products fetch working correctly')
    } else {
      console.log('❌ Filtered products fetch issue')
      return false
    }

    // Test 3: Categories fetch
    console.log('\n3️⃣ Testing categories fetch...')
    const { GET: categoriesGET } = await import('../app/api/e-commerce/categories/route')
    
    const url3 = new URL('http://localhost:3090/api/e-commerce/categories')
    const request3 = new Request(url3)
    const response3 = await categoriesGET(request3)
    const data3 = await response3.json()
    
    console.log('📊 Categories results:')
    console.log('  - success:', data3.success)
    console.log('  - categories count:', data3.data?.length || 0)
    
    if (data3.success && Array.isArray(data3.data)) {
      console.log('✅ Categories fetch working correctly')
    } else {
      console.log('❌ Categories fetch issue')
      return false
    }

    // Test 4: Search products
    console.log('\n4️⃣ Testing product search...')
    const url4 = new URL('http://localhost:3090/api/e-commerce/products?query=kids&page=1&limit=5')
    const request4 = new Request(url4)
    const response4 = await productsGET(request4)
    const data4 = await response4.json()
    
    console.log('📊 Search results:')
    console.log('  - products found:', data4.data?.data?.length || 0)
    console.log('  - search term in results:', data4.data?.data?.some((p: any) => 
      p.title.toLowerCase().includes('kids') || 
      p.description?.toLowerCase().includes('kids')
    ) || false)
    
    if (data4.success) {
      console.log('✅ Product search working correctly')
    } else {
      console.log('❌ Product search issue')
      return false
    }

    // Test 5: Pagination
    console.log('\n5️⃣ Testing pagination...')
    const url5 = new URL('http://localhost:3090/api/e-commerce/products?page=2&limit=5')
    const request5 = new Request(url5)
    const response5 = await productsGET(request5)
    const data5 = await response5.json()
    
    console.log('📊 Pagination results:')
    console.log('  - page:', data5.data?.pagination?.page)
    console.log('  - limit:', data5.data?.pagination?.limit)
    console.log('  - hasNext:', data5.data?.pagination?.hasNext)
    console.log('  - hasPrev:', data5.data?.pagination?.hasPrev)
    
    if (data5.success && data5.data?.pagination?.page === 2) {
      console.log('✅ Pagination working correctly')
    } else {
      console.log('❌ Pagination issue')
      return false
    }

    return true

  } catch (error) {
    console.error('❌ Test failed:', error)
    return false
  }
}

async function runTests() {
  console.log('🚀 Frontend API Compatibility Tests\n')
  
  const success = await testFrontendApiCalls()
  
  if (success) {
    console.log('\n🎉 All tests passed! Frontend should now load products correctly.')
    console.log('\n📝 Next steps:')
    console.log('   1. Start dev server: pnpm dev')
    console.log('   2. Visit: http://localhost:3090/admin/products')
    console.log('   3. Products should now load and display properly')
  } else {
    console.log('\n⚠️  Some tests failed. Check the errors above.')
  }
}

runTests().catch(console.error)
