#!/usr/bin/env tsx

/**
 * Test script to verify the complete products frontend flow
 */

import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function testProductsFrontend() {
  console.log('🧪 Testing Products Frontend Flow\n')

  try {
    // 1. Test database connection and products
    console.log('1️⃣ Testing database...')
    const productCount = await prisma.product.count()
    console.log(`   ✅ Found ${productCount} products in database`)

    if (productCount === 0) {
      console.log('   ⚠️  No products found. Run seeder: pnpm seed:products')
      return
    }

    // 2. Test product service
    console.log('\n2️⃣ Testing ProductService...')
    const { productService } = await import('../lib/ecommerce')
    
    const searchResult = await productService().searchProducts({
      page: 1,
      limit: 5
    })
    
    if (searchResult.success) {
      console.log(`   ✅ ProductService working - ${searchResult.data?.data?.length} products`)
    } else {
      console.log(`   ❌ ProductService error: ${searchResult.error?.message}`)
      return
    }

    // 3. Test API route structure
    console.log('\n3️⃣ Testing API routes...')
    const fs = await import('fs')
    const path = await import('path')
    
    const requiredRoutes = [
      'app/api/e-commerce/products/route.ts',
      'app/api/e-commerce/products/[id]/route.ts',
      'app/api/e-commerce/categories/route.ts'
    ]
    
    let allRoutesExist = true
    for (const route of requiredRoutes) {
      const routePath = path.join(process.cwd(), route)
      if (fs.existsSync(routePath)) {
        console.log(`   ✅ ${route}`)
      } else {
        console.log(`   ❌ Missing: ${route}`)
        allRoutesExist = false
      }
    }

    // 4. Test component files
    console.log('\n4️⃣ Testing component files...')
    const requiredComponents = [
      'components/admin/products/enhanced-product-list.tsx',
      'components/admin/products/products-management-page.tsx',
      'components/admin/products/bulk-product-operations.tsx',
      'lib/ecommerce/hooks/use-products.ts',
      'lib/ecommerce/hooks/use-categories.ts'
    ]
    
    let allComponentsExist = true
    for (const component of requiredComponents) {
      const componentPath = path.join(process.cwd(), component)
      if (fs.existsSync(componentPath)) {
        console.log(`   ✅ ${component}`)
      } else {
        console.log(`   ❌ Missing: ${component}`)
        allComponentsExist = false
      }
    }

    // 5. Test sample product data
    console.log('\n5️⃣ Testing sample product data...')
    const sampleProduct = await prisma.product.findFirst({
      include: {
        images: true,
        categories: {
          include: {
            category: true
          }
        }
      }
    })

    if (sampleProduct) {
      console.log(`   ✅ Sample product: ${sampleProduct.title}`)
      console.log(`   📦 Price: ${sampleProduct.currency} ${sampleProduct.price}`)
      console.log(`   🏷️  Status: ${sampleProduct.status}`)
      console.log(`   🖼️  Images: ${sampleProduct.images.length}`)
      console.log(`   📂 Categories: ${sampleProduct.categories.length}`)
    }

    // Summary
    console.log('\n📊 Test Summary:')
    console.log(`   Database: ${productCount > 0 ? '✅' : '❌'} (${productCount} products)`)
    console.log(`   ProductService: ${searchResult.success ? '✅' : '❌'}`)
    console.log(`   API Routes: ${allRoutesExist ? '✅' : '❌'}`)
    console.log(`   Components: ${allComponentsExist ? '✅' : '❌'}`)

    if (productCount > 0 && searchResult.success && allRoutesExist && allComponentsExist) {
      console.log('\n🎉 All tests passed! Products frontend should be working.')
      console.log('\n📝 Next steps:')
      console.log('   1. Start dev server: pnpm dev')
      console.log('   2. Visit: http://localhost:3090/admin/products')
      console.log('   3. Or debug: http://localhost:3090/admin/products/debug')
    } else {
      console.log('\n⚠️  Some issues found. Check the errors above.')
    }

  } catch (error) {
    console.error('❌ Test failed:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the test
testProductsFrontend().catch(console.error)
