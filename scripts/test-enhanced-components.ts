#!/usr/bin/env tsx

/**
 * Test all enhanced admin components API endpoints
 */

import { NextRequest } from 'next/server'

async function testOrdersAPI() {
  console.log('🛒 Testing Orders API...')
  
  try {
    const { GET } = await import('../app/api/e-commerce/orders/route')
    
    const url = new URL('http://localhost:3090/api/e-commerce/orders?page=1&limit=5')
    const request = new NextRequest(url, {
      headers: { 'x-admin-request': 'true' }
    })
    
    const response = await GET(request)
    const data = await response.json()
    
    console.log('📊 Orders API Response:')
    console.log('  - success:', data.success)
    console.log('  - data.data exists:', !!data.data?.data)
    console.log('  - data.pagination exists:', !!data.data?.pagination)
    console.log('  - orders count:', data.data?.data?.length || 0)
    console.log('  - total orders:', data.data?.pagination?.total || 0)
    
    if (data.success && data.data?.data && data.data?.pagination) {
      console.log('✅ Orders API working correctly')
      return true
    } else {
      console.log('❌ Orders API format issue')
      return false
    }
  } catch (error) {
    console.error('❌ Orders API error:', error)
    return false
  }
}

async function testCustomersAPI() {
  console.log('\n👥 Testing Customers API...')
  
  try {
    const { GET } = await import('../app/api/e-commerce/customers/route')
    
    const url = new URL('http://localhost:3090/api/e-commerce/customers?page=1&limit=5')
    const request = new NextRequest(url)
    
    const response = await GET(request)
    const data = await response.json()
    
    console.log('📊 Customers API Response:')
    console.log('  - success:', data.success)
    console.log('  - data.data exists:', !!data.data?.data)
    console.log('  - data.pagination exists:', !!data.data?.pagination)
    console.log('  - customers count:', data.data?.data?.length || 0)
    console.log('  - total customers:', data.data?.pagination?.total || 0)
    
    if (data.success && data.data?.data && data.data?.pagination) {
      console.log('✅ Customers API working correctly')
      return true
    } else {
      console.log('❌ Customers API format issue')
      return false
    }
  } catch (error) {
    console.error('❌ Customers API error:', error)
    return false
  }
}

async function testInventoryAPI() {
  console.log('\n📦 Testing Inventory API...')
  
  try {
    const { GET } = await import('../app/api/inventory/route')
    
    const url = new URL('http://localhost:3090/api/inventory')
    const request = new NextRequest(url)
    
    const response = await GET(request)
    const data = await response.json()
    
    console.log('📊 Inventory API Response:')
    console.log('  - success:', data.success)
    console.log('  - data.data exists:', !!data.data?.data)
    console.log('  - data.pagination exists:', !!data.data?.pagination)
    console.log('  - inventory items count:', data.data?.data?.length || 0)
    console.log('  - total items:', data.data?.pagination?.total || 0)
    
    if (data.success && data.data?.data && data.data?.pagination) {
      console.log('✅ Inventory API working correctly')
      return true
    } else {
      console.log('❌ Inventory API format issue')
      return false
    }
  } catch (error) {
    console.error('❌ Inventory API error:', error)
    return false
  }
}

async function testFulfillmentsAPI() {
  console.log('\n🚚 Testing Fulfillments API...')
  
  try {
    const { GET } = await import('../app/api/e-commerce/fulfillments/route')
    
    const url = new URL('http://localhost:3090/api/e-commerce/fulfillments?page=1&limit=5')
    const request = new NextRequest(url, {
      headers: { 'x-admin-request': 'true' }
    })
    
    const response = await GET(request)
    const data = await response.json()
    
    console.log('📊 Fulfillments API Response:')
    console.log('  - success:', data.success)
    console.log('  - data.data exists:', !!data.data?.data)
    console.log('  - data.pagination exists:', !!data.data?.pagination)
    console.log('  - fulfillments count:', data.data?.data?.length || 0)
    console.log('  - total fulfillments:', data.data?.pagination?.total || 0)
    
    if (data.success && data.data?.data && data.data?.pagination) {
      console.log('✅ Fulfillments API working correctly')
      return true
    } else {
      console.log('❌ Fulfillments API format issue')
      return false
    }
  } catch (error) {
    console.error('❌ Fulfillments API error:', error)
    return false
  }
}

async function testProductsAPI() {
  console.log('\n🛍️ Testing Products API (already fixed)...')
  
  try {
    const { GET } = await import('../app/api/e-commerce/products/route')
    
    const url = new URL('http://localhost:3090/api/e-commerce/products?page=1&limit=5')
    const request = new NextRequest(url)
    
    const response = await GET(request)
    const data = await response.json()
    
    console.log('📊 Products API Response:')
    console.log('  - success:', data.success)
    console.log('  - data.data exists:', !!data.data?.data)
    console.log('  - data.pagination exists:', !!data.data?.pagination)
    console.log('  - products count:', data.data?.data?.length || 0)
    console.log('  - total products:', data.data?.pagination?.total || 0)
    
    if (data.success && data.data?.data && data.data?.pagination) {
      console.log('✅ Products API working correctly')
      return true
    } else {
      console.log('❌ Products API format issue')
      return false
    }
  } catch (error) {
    console.error('❌ Products API error:', error)
    return false
  }
}

async function runAllTests() {
  console.log('🚀 Testing Enhanced Admin Components APIs\n')
  
  const results = await Promise.all([
    testProductsAPI(),
    testOrdersAPI(),
    testCustomersAPI(),
    testInventoryAPI(),
    testFulfillmentsAPI()
  ])
  
  const passedTests = results.filter(Boolean).length
  const totalTests = results.length
  
  console.log('\n📋 Test Summary:')
  console.log(`✅ Passed: ${passedTests}/${totalTests} tests`)
  
  if (passedTests === totalTests) {
    console.log('\n🎉 All enhanced components are ready!')
    console.log('\n📝 Enhanced Components Created:')
    console.log('   1. ✅ Enhanced Products List (already working)')
    console.log('   2. ✅ Enhanced Orders List')
    console.log('   3. ✅ Enhanced Customers List')
    console.log('   4. ✅ Enhanced Inventory List')
    console.log('   5. ✅ Enhanced Fulfillments List')
    
    console.log('\n🎨 Features Added to Each Component:')
    console.log('   • Dashboard-style statistics cards')
    console.log('   • Advanced filtering and search')
    console.log('   • Table and grid view modes')
    console.log('   • Bulk operations')
    console.log('   • Enhanced pagination')
    console.log('   • Modern UI/UX with Shadcn components')
    console.log('   • Real-time data loading')
    console.log('   • Error handling and retry functionality')
    console.log('   • Responsive design')
    console.log('   • Export capabilities')
    
    console.log('\n🚀 Next Steps:')
    console.log('   1. Start dev server: pnpm dev')
    console.log('   2. Visit admin pages:')
    console.log('      - http://localhost:3090/admin/products')
    console.log('      - http://localhost:3090/admin/orders')
    console.log('      - http://localhost:3090/admin/customers')
    console.log('      - http://localhost:3090/admin/inventory')
    console.log('      - http://localhost:3090/admin/fulfillments')
    console.log('   3. All components should load with enhanced UI!')
    
  } else {
    console.log('\n⚠️  Some tests failed. Check the errors above.')
    console.log('   Failed tests need API response format fixes.')
  }
}

runAllTests().catch(console.error)
