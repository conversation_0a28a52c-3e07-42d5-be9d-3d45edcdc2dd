import { productService } from "@/lib/ecommerce"
import { ProductCard } from "@/components/storefront/products/product-card"

interface RelatedProductsProps {
  categoryId: string
  currentProductId: string
}

export async function RelatedProducts({ categoryId, currentProductId }: RelatedProductsProps) {
  try {
    const result = await productService().getRelatedProducts(currentProductId, 4)

    if (!result.success || !result.data || result.data.length === 0) {
      return null
    }

    return (
      <section>
        <h2 className="text-2xl font-bold font-montserrat mb-6">You May Also Like</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6">
          {result.data.map((product) => (
            <ProductCard key={product.id} product={product} />
          ))}
        </div>
      </section>
    )
  } catch (error) {
    console.error('Error fetching related products:', error)
    return null
  }
}
