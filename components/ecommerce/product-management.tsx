'use client'

import React, { useState } from 'react'
import { useProducts, useProductMutations } from '@/lib/ecommerce/hooks/use-products'
import { Product } from '@/lib/ecommerce/types'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Search, Filter, Plus, Eye, Edit, Trash2, Package, Star, DollarSign } from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import Image from 'next/image'

interface ProductManagementProps {
  className?: string
}

export function ProductManagement({ className }: ProductManagementProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [categoryFilter, setCategoryFilter] = useState<string>('')
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)
  const [isProductDialogOpen, setIsProductDialogOpen] = useState(false)

  const { 
    products, 
    loading, 
    error, 
    pagination, 
    searchProducts, 
    refetch 
  } = useProducts({
    initialParams: { page: 1, limit: 20 },
    autoFetch: true
  })

  const { 
    updateProduct, 
    deleteProduct, 
    loading: mutationLoading 
  } = useProductMutations()

  const handleSearch = () => {
    searchProducts({
      query: searchQuery,
      filters: {
        status: statusFilter ? [statusFilter as any] : undefined,
        categoryIds: categoryFilter ? [categoryFilter] : undefined
      },
      page: 1,
      limit: 20
    })
  }

  const handleDeleteProduct = async (productId: string) => {
    if (confirm('Are you sure you want to delete this product?')) {
      const success = await deleteProduct(productId)
      if (success) {
        refetch()
      }
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { variant: 'default' as const, color: 'bg-green-100 text-green-800' },
      draft: { variant: 'secondary' as const, color: 'bg-gray-100 text-gray-800' },
      archived: { variant: 'outline' as const, color: 'bg-red-100 text-red-800' }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft

    return (
      <Badge variant={config.variant}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  const openProductDetails = (product: Product) => {
    setSelectedProduct(product)
    setIsProductDialogOpen(true)
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            Error loading products: {error}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Product Management
            <div className="flex gap-2">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Product
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Search and Filters */}
          <div className="flex gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search products by title, SKU, or description..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Statuses</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="archived">Archived</SelectItem>
              </SelectContent>
            </Select>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Categories</SelectItem>
                <SelectItem value="clothing">Clothing</SelectItem>
                <SelectItem value="accessories">Accessories</SelectItem>
                <SelectItem value="toys">Toys</SelectItem>
                <SelectItem value="books">Books</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={handleSearch} disabled={loading}>
              <Filter className="h-4 w-4 mr-2" />
              Search
            </Button>
          </div>

          {/* Products Table */}
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>SKU</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Inventory</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      Loading products...
                    </TableCell>
                  </TableRow>
                ) : products.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      No products found
                    </TableCell>
                  </TableRow>
                ) : (
                  products.map((product) => (
                    <TableRow key={product.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          {product.images && product.images.length > 0 ? (
                            <Image
                              src={product.images[0].url}
                              alt={product.images[0].altText || product.title}
                              width={40}
                              height={40}
                              className="rounded object-cover"
                            />
                          ) : (
                            <div className="w-10 h-10 bg-gray-200 rounded flex items-center justify-center">
                              <Package className="h-5 w-5 text-gray-400" />
                            </div>
                          )}
                          <div>
                            <div className="font-medium">{product.title}</div>
                            <div className="text-sm text-gray-500">
                              {product.vendor}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="font-mono text-sm">
                        {product.handle}
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(product.status)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Package className="h-4 w-4 text-gray-400" />
                          <span className="text-sm">
                            {product.inventoryQuantity || 0}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {formatCurrency(product.price.amount, product.price.currency)}
                          </div>
                          {product.compareAtPrice && (
                            <div className="text-sm text-gray-500 line-through">
                              {formatCurrency(product.compareAtPrice.amount, product.compareAtPrice.currency)}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {new Date(product.createdAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openProductDetails(product)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteProduct(product.id)}
                            disabled={mutationLoading}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {pagination && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-gray-500">
                Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                {pagination.total} products
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  disabled={!pagination.hasPrev || loading}
                  onClick={() => searchProducts({ 
                    query: searchQuery,
                    filters: {
                      status: statusFilter ? [statusFilter as any] : undefined,
                      categoryIds: categoryFilter ? [categoryFilter] : undefined
                    },
                    page: pagination.page - 1 
                  })}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={!pagination.hasNext || loading}
                  onClick={() => searchProducts({ 
                    query: searchQuery,
                    filters: {
                      status: statusFilter ? [statusFilter as any] : undefined,
                      categoryIds: categoryFilter ? [categoryFilter] : undefined
                    },
                    page: pagination.page + 1 
                  })}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Product Details Dialog */}
      <Dialog open={isProductDialogOpen} onOpenChange={setIsProductDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              Product Details - {selectedProduct?.title}
            </DialogTitle>
          </DialogHeader>
          {selectedProduct && (
            <ProductDetailsView product={selectedProduct} />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

// Product Details Component
function ProductDetailsView({ product }: { product: Product }) {
  return (
    <Tabs defaultValue="overview" className="w-full">
      <TabsList className="grid w-full grid-cols-4">
        <TabsTrigger value="overview">Overview</TabsTrigger>
        <TabsTrigger value="variants">Variants</TabsTrigger>
        <TabsTrigger value="inventory">Inventory</TabsTrigger>
        <TabsTrigger value="seo">SEO</TabsTrigger>
      </TabsList>
      
      <TabsContent value="overview" className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Product Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Title:</span>
                <span className="text-sm font-medium">{product.title}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Handle:</span>
                <span className="text-sm font-mono">{product.handle}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Status:</span>
                <Badge variant="secondary">{product.status}</Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Vendor:</span>
                <span className="text-sm">{product.vendor}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Product Type:</span>
                <span className="text-sm">{product.productType}</span>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Pricing</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Price:</span>
                <span className="text-sm font-medium">
                  {formatCurrency(product.price.amount, product.price.currency)}
                </span>
              </div>
              {product.compareAtPrice && (
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Compare at price:</span>
                  <span className="text-sm line-through">
                    {formatCurrency(product.compareAtPrice.amount, product.compareAtPrice.currency)}
                  </span>
                </div>
              )}
              {product.costPerItem && (
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Cost per item:</span>
                  <span className="text-sm">
                    {formatCurrency(product.costPerItem.amount, product.costPerItem.currency)}
                  </span>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Description</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-sm text-gray-700">
              {product.description}
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="variants" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Product Variants</CardTitle>
          </CardHeader>
          <CardContent>
            {product.variants && product.variants.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Title</TableHead>
                    <TableHead>SKU</TableHead>
                    <TableHead>Price</TableHead>
                    <TableHead>Inventory</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {product.variants.map((variant) => (
                    <TableRow key={variant.id}>
                      <TableCell>{variant.title}</TableCell>
                      <TableCell className="font-mono text-sm">{variant.sku}</TableCell>
                      <TableCell>
                        {formatCurrency(variant.price.amount, variant.price.currency)}
                      </TableCell>
                      <TableCell>{variant.inventoryQuantity}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="text-sm text-gray-500">
                No variants configured for this product.
              </div>
            )}
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="inventory" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Inventory Management</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Track quantity:</span>
              <span className="text-sm">{product.trackQuantity ? 'Yes' : 'No'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Continue selling when out of stock:</span>
              <span className="text-sm">{product.continueSellingWhenOutOfStock ? 'Yes' : 'No'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Current inventory:</span>
              <span className="text-sm font-medium">{product.inventoryQuantity || 0}</span>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="seo" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">SEO Settings</CardTitle>
          </CardHeader>
          <CardContent>
            {product.seo ? (
              <div className="space-y-2">
                <div>
                  <span className="text-sm text-gray-500">Title:</span>
                  <div className="text-sm">{product.seo.title}</div>
                </div>
                <div>
                  <span className="text-sm text-gray-500">Description:</span>
                  <div className="text-sm">{product.seo.description}</div>
                </div>
                {product.seo.keywords && (
                  <div>
                    <span className="text-sm text-gray-500">Keywords:</span>
                    <div className="text-sm">{product.seo.keywords.join(', ')}</div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-sm text-gray-500">
                No SEO settings configured for this product.
              </div>
            )}
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  )
}
