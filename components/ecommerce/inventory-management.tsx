'use client'

import React, { useState } from 'react'
import { useInventory, useInventoryMutations } from '@/lib/ecommerce/hooks/use-inventory'
import { InventoryItem } from '@/lib/ecommerce/services/inventory-service'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Search, Filter, Plus, Package, AlertTriangle, TrendingUp, TrendingDown, Edit } from 'lucide-react'
import { formatCurrency } from '@/lib/utils'

interface InventoryManagementProps {
  className?: string
}

export function InventoryManagement({ className }: InventoryManagementProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [locationFilter, setLocationFilter] = useState<string>('')
  const [stockFilter, setStockFilter] = useState<string>('')
  const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null)
  const [isAdjustmentDialogOpen, setIsAdjustmentDialogOpen] = useState(false)
  const [adjustmentQuantity, setAdjustmentQuantity] = useState('')
  const [adjustmentReason, setAdjustmentReason] = useState('')

  const { 
    inventory, 
    loading, 
    error, 
    pagination, 
    searchInventory, 
    refetch 
  } = useInventory({
    initialParams: { page: 1, limit: 20 },
    autoFetch: true
  })

  const { 
    adjustInventory, 
    updateInventory, 
    loading: mutationLoading 
  } = useInventoryMutations()

  const handleSearch = () => {
    searchInventory({
      query: searchQuery,
      filters: {
        location: locationFilter || undefined,
        stockLevel: stockFilter || undefined
      },
      page: 1,
      limit: 20
    })
  }

  const handleAdjustment = async () => {
    if (!selectedItem || !adjustmentQuantity || !adjustmentReason) return

    const adjustment = parseInt(adjustmentQuantity)
    const success = await adjustInventory(
      selectedItem.productId,
      selectedItem.variantId,
      adjustment,
      adjustmentReason
    )

    if (success) {
      setIsAdjustmentDialogOpen(false)
      setAdjustmentQuantity('')
      setAdjustmentReason('')
      setSelectedItem(null)
      refetch()
    }
  }

  const getStockLevelBadge = (item: InventoryItem) => {
    if (item.quantity <= 0) {
      return <Badge variant="destructive">Out of Stock</Badge>
    } else if (item.quantity <= item.reorderLevel) {
      return <Badge variant="secondary">Low Stock</Badge>
    } else {
      return <Badge variant="default">In Stock</Badge>
    }
  }

  const openAdjustmentDialog = (item: InventoryItem) => {
    setSelectedItem(item)
    setIsAdjustmentDialogOpen(true)
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            Error loading inventory: {error}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Inventory Management
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <TrendingUp className="h-4 w-4 mr-2" />
                Stock Report
              </Button>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Item
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Search and Filters */}
          <div className="flex gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search inventory by SKU, product name..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
            </div>
            <Select value={stockFilter} onValueChange={setStockFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by stock level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Stock Levels</SelectItem>
                <SelectItem value="in-stock">In Stock</SelectItem>
                <SelectItem value="low-stock">Low Stock</SelectItem>
                <SelectItem value="out-of-stock">Out of Stock</SelectItem>
              </SelectContent>
            </Select>
            <Select value={locationFilter} onValueChange={setLocationFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by location" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Locations</SelectItem>
                <SelectItem value="main-warehouse">Main Warehouse</SelectItem>
                <SelectItem value="store-front">Store Front</SelectItem>
                <SelectItem value="online-only">Online Only</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={handleSearch} disabled={loading}>
              <Filter className="h-4 w-4 mr-2" />
              Search
            </Button>
          </div>

          {/* Inventory Stats */}
          <div className="grid grid-cols-4 gap-4 mb-6">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Package className="h-5 w-5 text-blue-500" />
                  <div>
                    <div className="text-sm text-gray-500">Total Items</div>
                    <div className="text-2xl font-bold">{inventory.length}</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-yellow-500" />
                  <div>
                    <div className="text-sm text-gray-500">Low Stock</div>
                    <div className="text-2xl font-bold">
                      {inventory.filter(item => item.quantity <= item.reorderLevel && item.quantity > 0).length}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <TrendingDown className="h-5 w-5 text-red-500" />
                  <div>
                    <div className="text-sm text-gray-500">Out of Stock</div>
                    <div className="text-2xl font-bold">
                      {inventory.filter(item => item.quantity <= 0).length}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-green-500" />
                  <div>
                    <div className="text-sm text-gray-500">Total Value</div>
                    <div className="text-2xl font-bold">
                      {formatCurrency(
                        inventory.reduce((sum, item) => sum + (item.quantity * item.cost), 0),
                        'ZAR'
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Inventory Table */}
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>SKU</TableHead>
                  <TableHead>Product</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>On Hand</TableHead>
                  <TableHead>Available</TableHead>
                  <TableHead>Reserved</TableHead>
                  <TableHead>Reorder Level</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-8">
                      Loading inventory...
                    </TableCell>
                  </TableRow>
                ) : inventory.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-8">
                      No inventory items found
                    </TableCell>
                  </TableRow>
                ) : (
                  inventory.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-mono text-sm">
                        {item.sku}
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">Product {item.productId}</div>
                        {item.variantId && (
                          <div className="text-sm text-gray-500">Variant: {item.variantId}</div>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">{item.location || 'Main Warehouse'}</div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{item.quantity}</div>
                      </TableCell>
                      <TableCell>
                        <div className="text-green-600 font-medium">{item.available}</div>
                      </TableCell>
                      <TableCell>
                        <div className="text-orange-600">{item.reserved}</div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">{item.reorderLevel}</div>
                      </TableCell>
                      <TableCell>
                        {getStockLevelBadge(item)}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openAdjustmentDialog(item)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {pagination && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-gray-500">
                Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                {pagination.total} items
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  disabled={!pagination.hasPrev || loading}
                  onClick={() => searchInventory({ 
                    query: searchQuery,
                    filters: {
                      location: locationFilter || undefined,
                      stockLevel: stockFilter || undefined
                    },
                    page: pagination.page - 1 
                  })}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={!pagination.hasNext || loading}
                  onClick={() => searchInventory({ 
                    query: searchQuery,
                    filters: {
                      location: locationFilter || undefined,
                      stockLevel: stockFilter || undefined
                    },
                    page: pagination.page + 1 
                  })}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Stock Adjustment Dialog */}
      <Dialog open={isAdjustmentDialogOpen} onOpenChange={setIsAdjustmentDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              Adjust Stock - {selectedItem?.sku}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Current Quantity</label>
              <div className="text-2xl font-bold">{selectedItem?.quantity || 0}</div>
            </div>
            
            <div>
              <label className="text-sm font-medium">Adjustment Quantity</label>
              <Input
                type="number"
                placeholder="Enter positive or negative number"
                value={adjustmentQuantity}
                onChange={(e) => setAdjustmentQuantity(e.target.value)}
              />
              <div className="text-xs text-gray-500 mt-1">
                Use positive numbers to increase stock, negative to decrease
              </div>
            </div>
            
            <div>
              <label className="text-sm font-medium">Reason</label>
              <Select value={adjustmentReason} onValueChange={setAdjustmentReason}>
                <SelectTrigger>
                  <SelectValue placeholder="Select reason for adjustment" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="received">Stock Received</SelectItem>
                  <SelectItem value="damaged">Damaged Goods</SelectItem>
                  <SelectItem value="lost">Lost/Stolen</SelectItem>
                  <SelectItem value="returned">Customer Return</SelectItem>
                  <SelectItem value="correction">Inventory Correction</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {adjustmentQuantity && (
              <div className="p-3 bg-gray-50 rounded-lg">
                <div className="text-sm">
                  <div className="flex justify-between">
                    <span>Current:</span>
                    <span className="font-medium">{selectedItem?.quantity || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Adjustment:</span>
                    <span className={`font-medium ${parseInt(adjustmentQuantity) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {parseInt(adjustmentQuantity) >= 0 ? '+' : ''}{adjustmentQuantity}
                    </span>
                  </div>
                  <div className="flex justify-between border-t pt-2 mt-2">
                    <span>New Total:</span>
                    <span className="font-bold">
                      {(selectedItem?.quantity || 0) + parseInt(adjustmentQuantity || '0')}
                    </span>
                  </div>
                </div>
              </div>
            )}
            
            <div className="flex gap-2 justify-end">
              <Button
                variant="outline"
                onClick={() => setIsAdjustmentDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleAdjustment}
                disabled={!adjustmentQuantity || !adjustmentReason || mutationLoading}
              >
                Apply Adjustment
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
