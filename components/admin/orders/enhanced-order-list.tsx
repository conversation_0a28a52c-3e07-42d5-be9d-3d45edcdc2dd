'use client'

import { useState, useEffect, useMemo, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  Search, 
  Plus, 
  Filter, 
  MoreHorizontal, 
  Edit, 
  Eye,
  Package,
  TrendingUp,
  AlertTriangle,
  Loader2,
  Grid3X3,
  List,
  SortAsc,
  SortDesc,
  Download,
  Upload,
  Settings,
  RefreshCw,
  Star,
  ShoppingCart,
  DollarSign,
  BarChart3,
  Calendar,
  Tag,
  User,
  CreditCard,
  Truck,
  CheckCircle2,
  XCircle,
  Clock,
  Zap,
  MapPin,
  Phone,
  Mail
} from 'lucide-react'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger, DropdownMenuCheckboxItem, DropdownMenuLabel } from '@/components/ui/dropdown-menu'
import { useOrders } from '@/lib/ecommerce/hooks/use-orders'
import { formatCurrency } from '@/lib/utils'
import type { Order } from '@/lib/ecommerce/types'

type ViewMode = 'table' | 'grid' | 'compact'
type FilterPreset = 'all' | 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled'

interface EnhancedOrderListProps {
  onCreateOrder: () => void
  onEditOrder: (order: Order) => void
  onViewOrder: (order: Order) => void
}

interface OrderFilters {
  status: string[]
  paymentStatus: string[]
  dateRange: { from: Date; to: Date } | null
  amountRange: { min: number; max: number } | null
  customerSearch: string
}

export function EnhancedOrderList({ 
  onCreateOrder, 
  onEditOrder, 
  onViewOrder 
}: EnhancedOrderListProps) {
  // View and layout state
  const [viewMode, setViewMode] = useState<ViewMode>('table')
  const [showFilters, setShowFilters] = useState(false)
  const [filterPreset, setFilterPreset] = useState<FilterPreset>('all')
  
  // Search and filtering state
  const [searchQuery, setSearchQuery] = useState('')
  const [debouncedSearch, setDebouncedSearch] = useState('')
  const [filters, setFilters] = useState<OrderFilters>({
    status: [],
    paymentStatus: [],
    dateRange: null,
    amountRange: null,
    customerSearch: ''
  })
  
  // Selection and bulk operations
  const [selectedOrders, setSelectedOrders] = useState<string[]>([])
  const [showBulkOperations, setShowBulkOperations] = useState(false)
  
  // Pagination and sorting
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(20)
  const [sortField, setSortField] = useState<'orderNumber' | 'total' | 'createdAt' | 'updatedAt' | 'customerEmail'>('createdAt')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')
  
  // UI state
  const [isRefreshing, setIsRefreshing] = useState(false)

  // Debounce search query
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setDebouncedSearch(searchQuery)
    }, 300)
    return () => clearTimeout(timeoutId)
  }, [searchQuery])

  // Build search parameters using useMemo to prevent unnecessary re-renders
  const searchParams = useMemo(() => ({
    query: debouncedSearch || undefined,
    filters: {
      status: filters.status.length > 0 ? filters.status : undefined,
      paymentStatus: filters.paymentStatus.length > 0 ? filters.paymentStatus : undefined,
      dateRange: filters.dateRange,
      amountRange: filters.amountRange,
      customerSearch: filters.customerSearch || undefined
    },
    sort: {
      field: sortField,
      direction: sortDirection
    },
    page: currentPage,
    limit: pageSize
  }), [debouncedSearch, filters, sortField, sortDirection, currentPage, pageSize])

  const { orders, loading, error, pagination, refetch, searchOrders } = useOrders({
    initialParams: searchParams
  })

  // Update search when parameters change
  useEffect(() => {
    searchOrders(searchParams)
  }, [searchParams, searchOrders])

  // Filter presets
  const applyFilterPreset = useCallback((preset: FilterPreset) => {
    setFilterPreset(preset)
    setCurrentPage(1)
    
    switch (preset) {
      case 'all':
        setFilters({
          status: [],
          paymentStatus: [],
          dateRange: null,
          amountRange: null,
          customerSearch: ''
        })
        break
      case 'pending':
        setFilters(prev => ({ ...prev, status: ['pending'] }))
        break
      case 'confirmed':
        setFilters(prev => ({ ...prev, status: ['confirmed'] }))
        break
      case 'processing':
        setFilters(prev => ({ ...prev, status: ['processing'] }))
        break
      case 'shipped':
        setFilters(prev => ({ ...prev, status: ['shipped'] }))
        break
      case 'delivered':
        setFilters(prev => ({ ...prev, status: ['delivered'] }))
        break
      case 'cancelled':
        setFilters(prev => ({ ...prev, status: ['cancelled'] }))
        break
    }
  }, [])

  // Enhanced refresh with loading state
  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true)
    try {
      await refetch()
    } finally {
      setIsRefreshing(false)
    }
  }, [refetch])

  // Selection handlers
  const handleSelectAll = useCallback((checked: boolean) => {
    if (checked) {
      setSelectedOrders(orders.map(order => order.id))
    } else {
      setSelectedOrders([])
    }
  }, [orders])

  const handleSelectOrder = useCallback((orderId: string, checked: boolean) => {
    if (checked) {
      setSelectedOrders(prev => [...prev, orderId])
    } else {
      setSelectedOrders(prev => prev.filter(id => id !== orderId))
    }
  }, [])

  // Status badge helper
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { variant: 'secondary' as const, label: 'Pending' },
      confirmed: { variant: 'default' as const, label: 'Confirmed' },
      processing: { variant: 'default' as const, label: 'Processing' },
      shipped: { variant: 'default' as const, label: 'Shipped' },
      delivered: { variant: 'default' as const, label: 'Delivered' },
      cancelled: { variant: 'destructive' as const, label: 'Cancelled' },
      refunded: { variant: 'outline' as const, label: 'Refunded' }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || { variant: 'outline' as const, label: status }
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  // Payment status badge helper
  const getPaymentStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { variant: 'secondary' as const, label: 'Pending' },
      paid: { variant: 'default' as const, label: 'Paid' },
      failed: { variant: 'destructive' as const, label: 'Failed' },
      refunded: { variant: 'outline' as const, label: 'Refunded' },
      partially_refunded: { variant: 'outline' as const, label: 'Partial Refund' }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || { variant: 'outline' as const, label: status }
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  // Statistics calculations
  const stats = useMemo(() => {
    const totalOrders = pagination?.total || 0
    const pendingOrders = orders.filter(o => o.status === 'pending').length
    const processingOrders = orders.filter(o => o.status === 'processing').length
    const shippedOrders = orders.filter(o => o.status === 'shipped').length
    const totalRevenue = orders.reduce((sum, o) => sum + o.total, 0)
    
    return {
      total: totalOrders,
      pending: pendingOrders,
      processing: processingOrders,
      shipped: shippedOrders,
      totalRevenue
    }
  }, [orders, pagination])

  return (
    <div className="space-y-6">
      {/* Enhanced Header with Stats */}
      <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Orders</h2>
            <p className="text-muted-foreground">
              Manage customer orders and fulfillment
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={handleRefresh} disabled={isRefreshing}>
              <RefreshCw className={`mr-2 h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Export
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem>Export as CSV</DropdownMenuItem>
                <DropdownMenuItem>Export as Excel</DropdownMenuItem>
                <DropdownMenuItem>Export as PDF</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <Button onClick={onCreateOrder}>
              <Plus className="mr-2 h-4 w-4" />
              Create Order
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <ShoppingCart className="h-4 w-4 text-blue-500" />
                <div>
                  <p className="text-sm font-medium">Total Orders</p>
                  <p className="text-2xl font-bold">{stats.total}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-yellow-500" />
                <div>
                  <p className="text-sm font-medium">Pending</p>
                  <p className="text-2xl font-bold">{stats.pending}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Package className="h-4 w-4 text-orange-500" />
                <div>
                  <p className="text-sm font-medium">Processing</p>
                  <p className="text-2xl font-bold">{stats.processing}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Truck className="h-4 w-4 text-blue-500" />
                <div>
                  <p className="text-sm font-medium">Shipped</p>
                  <p className="text-2xl font-bold">{stats.shipped}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <DollarSign className="h-4 w-4 text-green-500" />
                <div>
                  <p className="text-sm font-medium">Revenue</p>
                  <p className="text-2xl font-bold">{formatCurrency(stats.totalRevenue, 'ZAR')}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Filter Presets and Advanced Filters */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center">
              <Filter className="mr-2 h-5 w-5" />
              Filters & Search
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Settings className="mr-2 h-4 w-4" />
                Advanced
              </Button>
              <div className="flex items-center space-x-1">
                <Button
                  variant={viewMode === 'table' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('table')}
                >
                  <List className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Quick Filter Presets */}
          <div className="flex flex-wrap gap-2 mb-4">
            {(['all', 'pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'] as FilterPreset[]).map((preset) => (
              <Button
                key={preset}
                variant={filterPreset === preset ? 'default' : 'outline'}
                size="sm"
                onClick={() => applyFilterPreset(preset)}
              >
                {preset === 'all' && <ShoppingCart className="mr-2 h-3 w-3" />}
                {preset === 'pending' && <Clock className="mr-2 h-3 w-3" />}
                {preset === 'confirmed' && <CheckCircle2 className="mr-2 h-3 w-3" />}
                {preset === 'processing' && <Package className="mr-2 h-3 w-3" />}
                {preset === 'shipped' && <Truck className="mr-2 h-3 w-3" />}
                {preset === 'delivered' && <CheckCircle2 className="mr-2 h-3 w-3" />}
                {preset === 'cancelled' && <XCircle className="mr-2 h-3 w-3" />}
                {preset.charAt(0).toUpperCase() + preset.slice(1)}
              </Button>
            ))}
          </div>

          {/* Search and Basic Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search orders..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select
              value={filters.status.length === 1 ? filters.status[0] : 'all'}
              onValueChange={(value) => {
                if (value === 'all') {
                  setFilters(prev => ({ ...prev, status: [] }))
                } else {
                  setFilters(prev => ({ ...prev, status: [value] }))
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="All statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All statuses</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="confirmed">Confirmed</SelectItem>
                <SelectItem value="processing">Processing</SelectItem>
                <SelectItem value="shipped">Shipped</SelectItem>
                <SelectItem value="delivered">Delivered</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.paymentStatus.length === 1 ? filters.paymentStatus[0] : 'all'}
              onValueChange={(value) => {
                if (value === 'all') {
                  setFilters(prev => ({ ...prev, paymentStatus: [] }))
                } else {
                  setFilters(prev => ({ ...prev, paymentStatus: [value] }))
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Payment status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All payments</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="paid">Paid</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
                <SelectItem value="refunded">Refunded</SelectItem>
              </SelectContent>
            </Select>

            <Select value={`${sortField}-${sortDirection}`} onValueChange={(value) => {
              const [field, direction] = value.split('-')
              setSortField(field as 'orderNumber' | 'total' | 'createdAt' | 'updatedAt' | 'customerEmail')
              setSortDirection(direction as 'asc' | 'desc')
            }}>
              <SelectTrigger>
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="createdAt-desc">
                  <div className="flex items-center">
                    <Calendar className="mr-2 h-4 w-4" />
                    Newest first
                  </div>
                </SelectItem>
                <SelectItem value="createdAt-asc">
                  <div className="flex items-center">
                    <Calendar className="mr-2 h-4 w-4" />
                    Oldest first
                  </div>
                </SelectItem>
                <SelectItem value="total-desc">
                  <div className="flex items-center">
                    <DollarSign className="mr-2 h-4 w-4" />
                    Highest value
                  </div>
                </SelectItem>
                <SelectItem value="total-asc">
                  <div className="flex items-center">
                    <DollarSign className="mr-2 h-4 w-4" />
                    Lowest value
                  </div>
                </SelectItem>
                <SelectItem value="orderNumber-asc">
                  <div className="flex items-center">
                    <SortAsc className="mr-2 h-4 w-4" />
                    Order number A-Z
                  </div>
                </SelectItem>
                <SelectItem value="orderNumber-desc">
                  <div className="flex items-center">
                    <SortDesc className="mr-2 h-4 w-4" />
                    Order number Z-A
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Bulk Operations */}
      {selectedOrders.length > 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <ShoppingCart className="h-4 w-4" />
                <span className="font-medium">
                  {selectedOrders.length} order{selectedOrders.length !== 1 ? 's' : ''} selected
                </span>
              </div>
              <Button
                variant="outline"
                onClick={() => setShowBulkOperations(!showBulkOperations)}
              >
                <Zap className="mr-2 h-4 w-4" />
                Bulk Actions
              </Button>
            </div>
            {showBulkOperations && (
              <div className="mt-4 flex flex-wrap gap-2">
                <Button variant="outline" size="sm">Update Status</Button>
                <Button variant="outline" size="sm">Mark as Paid</Button>
                <Button variant="outline" size="sm">Send Notifications</Button>
                <Button variant="outline" size="sm">Export Selected</Button>
                <Button variant="outline" size="sm">Add Tags</Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button
              variant="outline"
              size="sm"
              className="ml-2"
              onClick={handleRefresh}
            >
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Enhanced Orders Display */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <ShoppingCart className="mr-2 h-5 w-5" />
                Orders
                {selectedOrders.length > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {selectedOrders.length} selected
                  </Badge>
                )}
              </CardTitle>
              <CardDescription>
                {pagination ? (
                  <div className="flex items-center space-x-4">
                    <span>{pagination.total} total orders</span>
                    <Separator orientation="vertical" className="h-4" />
                    <span>Page {pagination.page} of {pagination.totalPages}</span>
                    <Separator orientation="vertical" className="h-4" />
                    <Select value={pageSize.toString()} onValueChange={(value) => setPageSize(Number(value))}>
                      <SelectTrigger className="w-20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="10">10</SelectItem>
                        <SelectItem value="20">20</SelectItem>
                        <SelectItem value="50">50</SelectItem>
                        <SelectItem value="100">100</SelectItem>
                      </SelectContent>
                    </Select>
                    <span>per page</span>
                  </div>
                ) : 'Loading orders...'}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                <p className="text-sm text-muted-foreground">Loading orders...</p>
              </div>
            </div>
          ) : orders.length === 0 ? (
            <div className="text-center py-12">
              <ShoppingCart className="mx-auto h-16 w-16 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No orders found</h3>
              <p className="text-sm text-muted-foreground mb-6">
                {searchQuery || filters.status.length > 0 || filters.paymentStatus.length > 0
                  ? 'Try adjusting your search or filters to find what you\'re looking for.'
                  : 'No orders have been placed yet.'}
              </p>
              <div className="flex items-center justify-center space-x-2">
                {(searchQuery || filters.status.length > 0 || filters.paymentStatus.length > 0) && (
                  <Button variant="outline" onClick={() => {
                    setSearchQuery('')
                    setFilters({
                      status: [],
                      paymentStatus: [],
                      dateRange: null,
                      amountRange: null,
                      customerSearch: ''
                    })
                    setFilterPreset('all')
                  }}>
                    Clear Filters
                  </Button>
                )}
                <Button onClick={onCreateOrder}>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Order
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {viewMode === 'table' ? (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12">
                          <Checkbox
                            checked={selectedOrders.length === orders.length}
                            onCheckedChange={handleSelectAll}
                          />
                        </TableHead>
                        <TableHead>Order</TableHead>
                        <TableHead>Customer</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Payment</TableHead>
                        <TableHead>Total</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead className="w-12"></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {orders.map((order) => (
                        <TableRow key={order.id} className="group hover:bg-muted/50">
                          <TableCell>
                            <Checkbox
                              checked={selectedOrders.includes(order.id)}
                              onCheckedChange={(checked) =>
                                handleSelectOrder(order.id, checked as boolean)
                              }
                            />
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">#{order.orderNumber}</div>
                              <div className="text-sm text-muted-foreground">
                                {order.items?.length || 0} items
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">{order.customerEmail}</div>
                              {order.customerPhone && (
                                <div className="text-sm text-muted-foreground">
                                  {order.customerPhone}
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>{getStatusBadge(order.status)}</TableCell>
                          <TableCell>{getPaymentStatusBadge(order.paymentStatus)}</TableCell>
                          <TableCell>
                            <div className="font-medium">
                              {formatCurrency(order.total, order.currency)}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {new Date(order.createdAt).toLocaleDateString()}
                            </div>
                          </TableCell>
                          <TableCell>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => onViewOrder(order)}>
                                  <Eye className="mr-2 h-4 w-4" />
                                  View
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => onEditOrder(order)}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <Download className="mr-2 h-4 w-4" />
                                  Download Invoice
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {orders.map((order) => (
                    <Card key={order.id} className="group hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <div>
                              <h3 className="font-medium">#{order.orderNumber}</h3>
                              <p className="text-sm text-muted-foreground">
                                {order.items?.length || 0} items
                              </p>
                            </div>
                            <Checkbox
                              checked={selectedOrders.includes(order.id)}
                              onCheckedChange={(checked) =>
                                handleSelectOrder(order.id, checked as boolean)
                              }
                            />
                          </div>

                          <div className="space-y-2">
                            <div className="flex items-center space-x-2">
                              <User className="h-4 w-4 text-muted-foreground" />
                              <span className="text-sm">{order.customerEmail}</span>
                            </div>
                            {order.customerPhone && (
                              <div className="flex items-center space-x-2">
                                <Phone className="h-4 w-4 text-muted-foreground" />
                                <span className="text-sm">{order.customerPhone}</span>
                              </div>
                            )}
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="space-y-1">
                              {getStatusBadge(order.status)}
                              {getPaymentStatusBadge(order.paymentStatus)}
                            </div>
                            <div className="text-right">
                              <div className="font-semibold">
                                {formatCurrency(order.total, order.currency)}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {new Date(order.createdAt).toLocaleDateString()}
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center justify-between pt-2">
                            <Button variant="outline" size="sm" onClick={() => onViewOrder(order)}>
                              <Eye className="mr-2 h-4 w-4" />
                              View
                            </Button>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => onEditOrder(order)}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Download className="mr-2 h-4 w-4" />
                                  Download Invoice
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}

              {/* Enhanced Pagination */}
              {pagination && pagination.totalPages > 1 && (
                <div className="flex flex-col sm:flex-row items-center justify-between gap-4 pt-4 border-t">
                  <div className="text-sm text-muted-foreground">
                    Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                    {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                    {pagination.total} orders
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(1)}
                      disabled={pagination.page === 1}
                    >
                      First
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(pagination.page - 1)}
                      disabled={!pagination.hasPrev}
                    >
                      Previous
                    </Button>

                    {/* Page Numbers */}
                    <div className="flex items-center space-x-1">
                      {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                        let pageNum
                        if (pagination.totalPages <= 5) {
                          pageNum = i + 1
                        } else if (pagination.page <= 3) {
                          pageNum = i + 1
                        } else if (pagination.page >= pagination.totalPages - 2) {
                          pageNum = pagination.totalPages - 4 + i
                        } else {
                          pageNum = pagination.page - 2 + i
                        }

                        return (
                          <Button
                            key={pageNum}
                            variant={pageNum === pagination.page ? "default" : "outline"}
                            size="sm"
                            onClick={() => setCurrentPage(pageNum)}
                            className="w-8 h-8 p-0"
                          >
                            {pageNum}
                          </Button>
                        )
                      })}
                    </div>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(pagination.page + 1)}
                      disabled={!pagination.hasNext}
                    >
                      Next
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(pagination.totalPages)}
                      disabled={pagination.page === pagination.totalPages}
                    >
                      Last
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
