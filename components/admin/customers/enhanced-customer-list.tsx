'use client'

import { useState, useEffect, useMemo, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  Search, 
  Plus, 
  Filter, 
  MoreHorizontal, 
  Edit, 
  Eye,
  Users,
  TrendingUp,
  AlertTriangle,
  Loader2,
  Grid3X3,
  List,
  SortAsc,
  SortDesc,
  Download,
  Settings,
  RefreshCw,
  Star,
  ShoppingCart,
  DollarSign,
  Calendar,
  Tag,
  User,
  Mail,
  Phone,
  MapPin,
  CheckCircle2,
  XCircle,
  Clock,
  Zap,
  UserPlus,
  Heart,
  Gift
} from 'lucide-react'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { formatCurrency } from '@/lib/utils'

type ViewMode = 'table' | 'grid' | 'compact'
type FilterPreset = 'all' | 'active' | 'new' | 'vip' | 'inactive' | 'blocked'

interface Customer {
  id: string
  email: string
  firstName?: string
  lastName?: string
  displayName?: string
  phone?: string
  avatar?: string
  isActive: boolean
  isBlocked: boolean
  acceptsMarketing: boolean
  loyaltyPoints?: number
  loyaltyTier?: string
  totalSpent: number
  orderCount: number
  lastOrderAt?: string
  createdAt: string
  updatedAt: string
  tags?: string[]
}

interface EnhancedCustomerListProps {
  onCreateCustomer: () => void
  onEditCustomer: (customer: Customer) => void
  onViewCustomer: (customer: Customer) => void
}

interface CustomerFilters {
  status: string[]
  loyaltyTier: string[]
  dateRange: { from: Date; to: Date } | null
  spentRange: { min: number; max: number } | null
  acceptsMarketing: boolean | null
}

export function EnhancedCustomerList({ 
  onCreateCustomer, 
  onEditCustomer, 
  onViewCustomer 
}: EnhancedCustomerListProps) {
  // View and layout state
  const [viewMode, setViewMode] = useState<ViewMode>('table')
  const [showFilters, setShowFilters] = useState(false)
  const [filterPreset, setFilterPreset] = useState<FilterPreset>('all')
  
  // Search and filtering state
  const [searchQuery, setSearchQuery] = useState('')
  const [debouncedSearch, setDebouncedSearch] = useState('')
  const [filters, setFilters] = useState<CustomerFilters>({
    status: [],
    loyaltyTier: [],
    dateRange: null,
    spentRange: null,
    acceptsMarketing: null
  })
  
  // Selection and bulk operations
  const [selectedCustomers, setSelectedCustomers] = useState<string[]>([])
  const [showBulkOperations, setShowBulkOperations] = useState(false)
  
  // Pagination and sorting
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(20)
  const [sortField, setSortField] = useState<'email' | 'totalSpent' | 'createdAt' | 'lastOrderAt' | 'orderCount'>('createdAt')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')
  
  // UI state
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState<any>(null)

  // Debounce search query
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setDebouncedSearch(searchQuery)
    }, 300)
    return () => clearTimeout(timeoutId)
  }, [searchQuery])

  // Fetch customers
  const fetchCustomers = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const queryParams = new URLSearchParams()
      if (debouncedSearch) queryParams.set('query', debouncedSearch)
      if (currentPage) queryParams.set('page', currentPage.toString())
      if (pageSize) queryParams.set('limit', pageSize.toString())
      if (sortField) queryParams.set('sortBy', sortField)
      if (sortDirection) queryParams.set('sortOrder', sortDirection)
      
      // Add filters
      if (filters.status.length > 0) queryParams.set('status', filters.status.join(','))
      if (filters.loyaltyTier.length > 0) queryParams.set('loyaltyTier', filters.loyaltyTier.join(','))
      if (filters.acceptsMarketing !== null) queryParams.set('acceptsMarketing', filters.acceptsMarketing.toString())

      const response = await fetch(`/api/e-commerce/customers?${queryParams.toString()}`)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const result = await response.json()

      if (result.success && result.data) {
        setCustomers(result.data.data || [])
        setPagination(result.data.pagination || null)
      } else {
        setError(result.error || 'Failed to fetch customers')
        setCustomers([])
        setPagination(null)
      }
    } catch (err) {
      console.error('Error fetching customers:', err)
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      setCustomers([])
      setPagination(null)
    } finally {
      setLoading(false)
    }
  }, [debouncedSearch, currentPage, pageSize, sortField, sortDirection, filters])

  // Update search when parameters change
  useEffect(() => {
    fetchCustomers()
  }, [fetchCustomers])

  // Filter presets
  const applyFilterPreset = useCallback((preset: FilterPreset) => {
    setFilterPreset(preset)
    setCurrentPage(1)
    
    switch (preset) {
      case 'all':
        setFilters({
          status: [],
          loyaltyTier: [],
          dateRange: null,
          spentRange: null,
          acceptsMarketing: null
        })
        break
      case 'active':
        setFilters(prev => ({ ...prev, status: ['active'] }))
        break
      case 'new':
        const weekAgo = new Date()
        weekAgo.setDate(weekAgo.getDate() - 7)
        setFilters(prev => ({ 
          ...prev, 
          dateRange: { from: weekAgo, to: new Date() }
        }))
        break
      case 'vip':
        setFilters(prev => ({ ...prev, loyaltyTier: ['vip', 'platinum'] }))
        break
      case 'inactive':
        setFilters(prev => ({ ...prev, status: ['inactive'] }))
        break
      case 'blocked':
        setFilters(prev => ({ ...prev, status: ['blocked'] }))
        break
    }
  }, [])

  // Enhanced refresh with loading state
  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true)
    try {
      await fetchCustomers()
    } finally {
      setIsRefreshing(false)
    }
  }, [fetchCustomers])

  // Selection handlers
  const handleSelectAll = useCallback((checked: boolean) => {
    if (checked) {
      setSelectedCustomers(customers.map(customer => customer.id))
    } else {
      setSelectedCustomers([])
    }
  }, [customers])

  const handleSelectCustomer = useCallback((customerId: string, checked: boolean) => {
    if (checked) {
      setSelectedCustomers(prev => [...prev, customerId])
    } else {
      setSelectedCustomers(prev => prev.filter(id => id !== customerId))
    }
  }, [])

  // Status badge helper
  const getStatusBadge = (customer: Customer) => {
    if (customer.isBlocked) {
      return <Badge variant="destructive">Blocked</Badge>
    }
    if (!customer.isActive) {
      return <Badge variant="secondary">Inactive</Badge>
    }
    return <Badge variant="default">Active</Badge>
  }

  // Loyalty tier badge helper
  const getLoyaltyTierBadge = (tier?: string) => {
    if (!tier) return null
    
    const tierConfig = {
      bronze: { variant: 'outline' as const, label: 'Bronze' },
      silver: { variant: 'secondary' as const, label: 'Silver' },
      gold: { variant: 'default' as const, label: 'Gold' },
      platinum: { variant: 'default' as const, label: 'Platinum' },
      vip: { variant: 'default' as const, label: 'VIP' }
    }

    const config = tierConfig[tier as keyof typeof tierConfig] || { variant: 'outline' as const, label: tier }
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  // Statistics calculations
  const stats = useMemo(() => {
    const totalCustomers = pagination?.total || 0
    const activeCustomers = customers.filter(c => c.isActive && !c.isBlocked).length
    const newCustomers = customers.filter(c => {
      const weekAgo = new Date()
      weekAgo.setDate(weekAgo.getDate() - 7)
      return new Date(c.createdAt) > weekAgo
    }).length
    const vipCustomers = customers.filter(c => c.loyaltyTier === 'vip' || c.loyaltyTier === 'platinum').length
    const totalRevenue = customers.reduce((sum, c) => sum + c.totalSpent, 0)
    
    return {
      total: totalCustomers,
      active: activeCustomers,
      new: newCustomers,
      vip: vipCustomers,
      totalRevenue
    }
  }, [customers, pagination])

  return (
    <div className="space-y-6">
      {/* Enhanced Header with Stats */}
      <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Customers</h2>
            <p className="text-muted-foreground">
              Manage customer relationships and data
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={handleRefresh} disabled={isRefreshing}>
              <RefreshCw className={`mr-2 h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Export
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem>Export as CSV</DropdownMenuItem>
                <DropdownMenuItem>Export as Excel</DropdownMenuItem>
                <DropdownMenuItem>Export Mailing List</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <Button onClick={onCreateCustomer}>
              <UserPlus className="mr-2 h-4 w-4" />
              Add Customer
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Users className="h-4 w-4 text-blue-500" />
                <div>
                  <p className="text-sm font-medium">Total Customers</p>
                  <p className="text-2xl font-bold">{stats.total}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <CheckCircle2 className="h-4 w-4 text-green-500" />
                <div>
                  <p className="text-sm font-medium">Active</p>
                  <p className="text-2xl font-bold">{stats.active}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <UserPlus className="h-4 w-4 text-blue-500" />
                <div>
                  <p className="text-sm font-medium">New (7 days)</p>
                  <p className="text-2xl font-bold">{stats.new}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Star className="h-4 w-4 text-yellow-500" />
                <div>
                  <p className="text-sm font-medium">VIP</p>
                  <p className="text-2xl font-bold">{stats.vip}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <DollarSign className="h-4 w-4 text-green-500" />
                <div>
                  <p className="text-sm font-medium">Total Spent</p>
                  <p className="text-2xl font-bold">{formatCurrency(stats.totalRevenue, 'ZAR')}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Filter Presets and Advanced Filters */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center">
              <Filter className="mr-2 h-5 w-5" />
              Filters & Search
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Settings className="mr-2 h-4 w-4" />
                Advanced
              </Button>
              <div className="flex items-center space-x-1">
                <Button
                  variant={viewMode === 'table' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('table')}
                >
                  <List className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Quick Filter Presets */}
          <div className="flex flex-wrap gap-2 mb-4">
            {(['all', 'active', 'new', 'vip', 'inactive', 'blocked'] as FilterPreset[]).map((preset) => (
              <Button
                key={preset}
                variant={filterPreset === preset ? 'default' : 'outline'}
                size="sm"
                onClick={() => applyFilterPreset(preset)}
              >
                {preset === 'all' && <Users className="mr-2 h-3 w-3" />}
                {preset === 'active' && <CheckCircle2 className="mr-2 h-3 w-3" />}
                {preset === 'new' && <UserPlus className="mr-2 h-3 w-3" />}
                {preset === 'vip' && <Star className="mr-2 h-3 w-3" />}
                {preset === 'inactive' && <Clock className="mr-2 h-3 w-3" />}
                {preset === 'blocked' && <XCircle className="mr-2 h-3 w-3" />}
                {preset.charAt(0).toUpperCase() + preset.slice(1)}
              </Button>
            ))}
          </div>

          {/* Search and Basic Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search customers..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select
              value={filters.status.length === 1 ? filters.status[0] : 'all'}
              onValueChange={(value) => {
                if (value === 'all') {
                  setFilters(prev => ({ ...prev, status: [] }))
                } else {
                  setFilters(prev => ({ ...prev, status: [value] }))
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="All statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All statuses</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="blocked">Blocked</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.loyaltyTier.length === 1 ? filters.loyaltyTier[0] : 'all'}
              onValueChange={(value) => {
                if (value === 'all') {
                  setFilters(prev => ({ ...prev, loyaltyTier: [] }))
                } else {
                  setFilters(prev => ({ ...prev, loyaltyTier: [value] }))
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Loyalty tier" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All tiers</SelectItem>
                <SelectItem value="bronze">Bronze</SelectItem>
                <SelectItem value="silver">Silver</SelectItem>
                <SelectItem value="gold">Gold</SelectItem>
                <SelectItem value="platinum">Platinum</SelectItem>
                <SelectItem value="vip">VIP</SelectItem>
              </SelectContent>
            </Select>

            <Select value={`${sortField}-${sortDirection}`} onValueChange={(value) => {
              const [field, direction] = value.split('-')
              setSortField(field as 'email' | 'totalSpent' | 'createdAt' | 'lastOrderAt' | 'orderCount')
              setSortDirection(direction as 'asc' | 'desc')
            }}>
              <SelectTrigger>
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="createdAt-desc">
                  <div className="flex items-center">
                    <Calendar className="mr-2 h-4 w-4" />
                    Newest first
                  </div>
                </SelectItem>
                <SelectItem value="createdAt-asc">
                  <div className="flex items-center">
                    <Calendar className="mr-2 h-4 w-4" />
                    Oldest first
                  </div>
                </SelectItem>
                <SelectItem value="totalSpent-desc">
                  <div className="flex items-center">
                    <DollarSign className="mr-2 h-4 w-4" />
                    Highest spender
                  </div>
                </SelectItem>
                <SelectItem value="totalSpent-asc">
                  <div className="flex items-center">
                    <DollarSign className="mr-2 h-4 w-4" />
                    Lowest spender
                  </div>
                </SelectItem>
                <SelectItem value="email-asc">
                  <div className="flex items-center">
                    <SortAsc className="mr-2 h-4 w-4" />
                    Email A-Z
                  </div>
                </SelectItem>
                <SelectItem value="email-desc">
                  <div className="flex items-center">
                    <SortDesc className="mr-2 h-4 w-4" />
                    Email Z-A
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Bulk Operations */}
      {selectedCustomers.length > 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Users className="h-4 w-4" />
                <span className="font-medium">
                  {selectedCustomers.length} customer{selectedCustomers.length !== 1 ? 's' : ''} selected
                </span>
              </div>
              <Button
                variant="outline"
                onClick={() => setShowBulkOperations(!showBulkOperations)}
              >
                <Zap className="mr-2 h-4 w-4" />
                Bulk Actions
              </Button>
            </div>
            {showBulkOperations && (
              <div className="mt-4 flex flex-wrap gap-2">
                <Button variant="outline" size="sm">Send Email</Button>
                <Button variant="outline" size="sm">Add Tags</Button>
                <Button variant="outline" size="sm">Update Tier</Button>
                <Button variant="outline" size="sm">Export Selected</Button>
                <Button variant="outline" size="sm">Block Customers</Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button
              variant="outline"
              size="sm"
              className="ml-2"
              onClick={handleRefresh}
            >
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Enhanced Customers Display */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <Users className="mr-2 h-5 w-5" />
                Customers
                {selectedCustomers.length > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {selectedCustomers.length} selected
                  </Badge>
                )}
              </CardTitle>
              <CardDescription>
                {pagination ? (
                  <div className="flex items-center space-x-4">
                    <span>{pagination.total} total customers</span>
                    <Separator orientation="vertical" className="h-4" />
                    <span>Page {pagination.page} of {pagination.totalPages}</span>
                    <Separator orientation="vertical" className="h-4" />
                    <Select value={pageSize.toString()} onValueChange={(value) => setPageSize(Number(value))}>
                      <SelectTrigger className="w-20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="10">10</SelectItem>
                        <SelectItem value="20">20</SelectItem>
                        <SelectItem value="50">50</SelectItem>
                        <SelectItem value="100">100</SelectItem>
                      </SelectContent>
                    </Select>
                    <span>per page</span>
                  </div>
                ) : 'Loading customers...'}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                <p className="text-sm text-muted-foreground">Loading customers...</p>
              </div>
            </div>
          ) : customers.length === 0 ? (
            <div className="text-center py-12">
              <Users className="mx-auto h-16 w-16 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No customers found</h3>
              <p className="text-sm text-muted-foreground mb-6">
                {searchQuery || filters.status.length > 0 || filters.loyaltyTier.length > 0
                  ? 'Try adjusting your search or filters to find what you\'re looking for.'
                  : 'No customers have been added yet.'}
              </p>
              <div className="flex items-center justify-center space-x-2">
                {(searchQuery || filters.status.length > 0 || filters.loyaltyTier.length > 0) && (
                  <Button variant="outline" onClick={() => {
                    setSearchQuery('')
                    setFilters({
                      status: [],
                      loyaltyTier: [],
                      dateRange: null,
                      spentRange: null,
                      acceptsMarketing: null
                    })
                    setFilterPreset('all')
                  }}>
                    Clear Filters
                  </Button>
                )}
                <Button onClick={onCreateCustomer}>
                  <UserPlus className="mr-2 h-4 w-4" />
                  Add Customer
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {viewMode === 'table' ? (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12">
                          <Checkbox
                            checked={selectedCustomers.length === customers.length}
                            onCheckedChange={handleSelectAll}
                          />
                        </TableHead>
                        <TableHead>Customer</TableHead>
                        <TableHead>Contact</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Orders</TableHead>
                        <TableHead>Spent</TableHead>
                        <TableHead>Tier</TableHead>
                        <TableHead>Joined</TableHead>
                        <TableHead className="w-12"></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {customers.map((customer) => (
                        <TableRow key={customer.id} className="group hover:bg-muted/50">
                          <TableCell>
                            <Checkbox
                              checked={selectedCustomers.includes(customer.id)}
                              onCheckedChange={(checked) =>
                                handleSelectCustomer(customer.id, checked as boolean)
                              }
                            />
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              <Avatar className="h-8 w-8">
                                <AvatarImage src={customer.avatar} />
                                <AvatarFallback>
                                  {customer.firstName?.[0] || customer.email[0].toUpperCase()}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <div className="font-medium">
                                  {customer.displayName || `${customer.firstName || ''} ${customer.lastName || ''}`.trim() || customer.email}
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  {customer.email}
                                </div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              {customer.phone && (
                                <div className="flex items-center text-sm">
                                  <Phone className="mr-1 h-3 w-3" />
                                  {customer.phone}
                                </div>
                              )}
                              {customer.acceptsMarketing && (
                                <div className="flex items-center text-sm text-green-600">
                                  <Mail className="mr-1 h-3 w-3" />
                                  Marketing
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>{getStatusBadge(customer)}</TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {customer.orderCount} orders
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="font-medium">
                              {formatCurrency(customer.totalSpent, 'ZAR')}
                            </div>
                          </TableCell>
                          <TableCell>
                            {getLoyaltyTierBadge(customer.loyaltyTier)}
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {new Date(customer.createdAt).toLocaleDateString()}
                            </div>
                          </TableCell>
                          <TableCell>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => onViewCustomer(customer)}>
                                  <Eye className="mr-2 h-4 w-4" />
                                  View
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => onEditCustomer(customer)}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <Mail className="mr-2 h-4 w-4" />
                                  Send Email
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {customers.map((customer) => (
                    <Card key={customer.id} className="group hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <Avatar className="h-10 w-10">
                                <AvatarImage src={customer.avatar} />
                                <AvatarFallback>
                                  {customer.firstName?.[0] || customer.email[0].toUpperCase()}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <h3 className="font-medium">
                                  {customer.displayName || `${customer.firstName || ''} ${customer.lastName || ''}`.trim() || customer.email}
                                </h3>
                                <p className="text-sm text-muted-foreground">
                                  {customer.email}
                                </p>
                              </div>
                            </div>
                            <Checkbox
                              checked={selectedCustomers.includes(customer.id)}
                              onCheckedChange={(checked) =>
                                handleSelectCustomer(customer.id, checked as boolean)
                              }
                            />
                          </div>

                          <div className="space-y-2">
                            {customer.phone && (
                              <div className="flex items-center space-x-2">
                                <Phone className="h-4 w-4 text-muted-foreground" />
                                <span className="text-sm">{customer.phone}</span>
                              </div>
                            )}
                            <div className="flex items-center space-x-2">
                              <ShoppingCart className="h-4 w-4 text-muted-foreground" />
                              <span className="text-sm">{customer.orderCount} orders</span>
                            </div>
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="space-y-1">
                              {getStatusBadge(customer)}
                              {getLoyaltyTierBadge(customer.loyaltyTier)}
                            </div>
                            <div className="text-right">
                              <div className="font-semibold">
                                {formatCurrency(customer.totalSpent, 'ZAR')}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {new Date(customer.createdAt).toLocaleDateString()}
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center justify-between pt-2">
                            <Button variant="outline" size="sm" onClick={() => onViewCustomer(customer)}>
                              <Eye className="mr-2 h-4 w-4" />
                              View
                            </Button>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => onEditCustomer(customer)}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Mail className="mr-2 h-4 w-4" />
                                  Send Email
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}

              {/* Enhanced Pagination */}
              {pagination && pagination.totalPages > 1 && (
                <div className="flex flex-col sm:flex-row items-center justify-between gap-4 pt-4 border-t">
                  <div className="text-sm text-muted-foreground">
                    Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                    {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                    {pagination.total} customers
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(1)}
                      disabled={pagination.page === 1}
                    >
                      First
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(pagination.page - 1)}
                      disabled={!pagination.hasPrev}
                    >
                      Previous
                    </Button>

                    {/* Page Numbers */}
                    <div className="flex items-center space-x-1">
                      {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                        let pageNum
                        if (pagination.totalPages <= 5) {
                          pageNum = i + 1
                        } else if (pagination.page <= 3) {
                          pageNum = i + 1
                        } else if (pagination.page >= pagination.totalPages - 2) {
                          pageNum = pagination.totalPages - 4 + i
                        } else {
                          pageNum = pagination.page - 2 + i
                        }

                        return (
                          <Button
                            key={pageNum}
                            variant={pageNum === pagination.page ? "default" : "outline"}
                            size="sm"
                            onClick={() => setCurrentPage(pageNum)}
                            className="w-8 h-8 p-0"
                          >
                            {pageNum}
                          </Button>
                        )
                      })}
                    </div>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(pagination.page + 1)}
                      disabled={!pagination.hasNext}
                    >
                      Next
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(pagination.totalPages)}
                      disabled={pagination.page === pagination.totalPages}
                    >
                      Last
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
