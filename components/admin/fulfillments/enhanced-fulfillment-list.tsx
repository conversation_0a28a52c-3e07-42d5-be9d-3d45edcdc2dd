'use client'

import { useState, useEffect, useMemo, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { 
  Search, 
  Plus, 
  Filter, 
  MoreHorizontal, 
  Edit, 
  Eye,
  Package,
  TrendingUp,
  AlertTriangle,
  Loader2,
  Grid3X3,
  List,
  SortAsc,
  SortDesc,
  Download,
  Settings,
  RefreshCw,
  Calendar,
  Tag,
  CheckCircle2,
  XCircle,
  Clock,
  Zap,
  Truck,
  ShoppingCart,
  DollarSign,
  MapPin,
  Phone,
  Mail,
  Package2,
  Plane,
  Ship,
  Activity,
  AlertCircle
} from 'lucide-react'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { formatCurrency } from '@/lib/utils'

type ViewMode = 'table' | 'grid' | 'compact'
type FilterPreset = 'all' | 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'delayed'

interface Fulfillment {
  id: string
  orderId: string
  orderNumber: string
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'returned'
  carrier: string
  service: string
  trackingNumber?: string
  trackingUrl?: string
  estimatedDelivery?: string
  actualDelivery?: string
  shippingCost: number
  currency: string
  locationName: string
  notes?: string
  notifyCustomer: boolean
  createdAt: string
  updatedAt: string
  order: {
    id: string
    orderNumber: string
    customerEmail: string
    customerFirstName?: string
    customerLastName?: string
    total: number
    currency: string
  }
  items: Array<{
    id: string
    orderItemId: string
    quantity: number
  }>
}

interface EnhancedFulfillmentListProps {
  onCreateFulfillment: () => void
  onEditFulfillment: (fulfillment: Fulfillment) => void
  onViewFulfillment: (fulfillment: Fulfillment) => void
  onTrackShipment: (fulfillment: Fulfillment) => void
}

interface FulfillmentFilters {
  status: string[]
  carrier: string[]
  dateRange: { from: Date; to: Date } | null
  location: string[]
  delayed: boolean | null
}

export function EnhancedFulfillmentList({ 
  onCreateFulfillment, 
  onEditFulfillment, 
  onViewFulfillment,
  onTrackShipment
}: EnhancedFulfillmentListProps) {
  // View and layout state
  const [viewMode, setViewMode] = useState<ViewMode>('table')
  const [showFilters, setShowFilters] = useState(false)
  const [filterPreset, setFilterPreset] = useState<FilterPreset>('all')
  
  // Search and filtering state
  const [searchQuery, setSearchQuery] = useState('')
  const [debouncedSearch, setDebouncedSearch] = useState('')
  const [filters, setFilters] = useState<FulfillmentFilters>({
    status: [],
    carrier: [],
    dateRange: null,
    location: [],
    delayed: null
  })
  
  // Selection and bulk operations
  const [selectedFulfillments, setSelectedFulfillments] = useState<string[]>([])
  const [showBulkOperations, setShowBulkOperations] = useState(false)
  
  // Pagination and sorting
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(20)
  const [sortField, setSortField] = useState<'orderNumber' | 'status' | 'createdAt' | 'estimatedDelivery' | 'carrier'>('createdAt')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')
  
  // UI state
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [fulfillments, setFulfillments] = useState<Fulfillment[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState<any>(null)

  // Debounce search query
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setDebouncedSearch(searchQuery)
    }, 300)
    return () => clearTimeout(timeoutId)
  }, [searchQuery])

  // Fetch fulfillments
  const fetchFulfillments = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const queryParams = new URLSearchParams()
      if (debouncedSearch) queryParams.set('query', debouncedSearch)
      if (currentPage) queryParams.set('page', currentPage.toString())
      if (pageSize) queryParams.set('limit', pageSize.toString())
      if (sortField) queryParams.set('sortBy', sortField)
      if (sortDirection) queryParams.set('sortOrder', sortDirection)
      
      // Add filters
      if (filters.status.length > 0) queryParams.set('status', filters.status.join(','))
      if (filters.carrier.length > 0) queryParams.set('carrier', filters.carrier.join(','))
      if (filters.location.length > 0) queryParams.set('location', filters.location.join(','))
      if (filters.delayed !== null) queryParams.set('delayed', filters.delayed.toString())

      const response = await fetch(`/api/e-commerce/fulfillments?${queryParams.toString()}`, {
        headers: { 'x-admin-request': 'true' }
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const result = await response.json()

      if (result.success && result.data) {
        setFulfillments(result.data.data || [])
        setPagination(result.data.pagination || null)
      } else {
        setError(result.error || 'Failed to fetch fulfillments')
        setFulfillments([])
        setPagination(null)
      }
    } catch (err) {
      console.error('Error fetching fulfillments:', err)
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      setFulfillments([])
      setPagination(null)
    } finally {
      setLoading(false)
    }
  }, [debouncedSearch, currentPage, pageSize, sortField, sortDirection, filters])

  // Update search when parameters change
  useEffect(() => {
    fetchFulfillments()
  }, [fetchFulfillments])

  // Filter presets
  const applyFilterPreset = useCallback((preset: FilterPreset) => {
    setFilterPreset(preset)
    setCurrentPage(1)
    
    switch (preset) {
      case 'all':
        setFilters({
          status: [],
          carrier: [],
          dateRange: null,
          location: [],
          delayed: null
        })
        break
      case 'pending':
        setFilters(prev => ({ ...prev, status: ['pending'] }))
        break
      case 'processing':
        setFilters(prev => ({ ...prev, status: ['processing'] }))
        break
      case 'shipped':
        setFilters(prev => ({ ...prev, status: ['shipped'] }))
        break
      case 'delivered':
        setFilters(prev => ({ ...prev, status: ['delivered'] }))
        break
      case 'cancelled':
        setFilters(prev => ({ ...prev, status: ['cancelled'] }))
        break
      case 'delayed':
        setFilters(prev => ({ ...prev, delayed: true }))
        break
    }
  }, [])

  // Enhanced refresh with loading state
  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true)
    try {
      await fetchFulfillments()
    } finally {
      setIsRefreshing(false)
    }
  }, [fetchFulfillments])

  // Selection handlers
  const handleSelectAll = useCallback((checked: boolean) => {
    if (checked) {
      setSelectedFulfillments(fulfillments.map(fulfillment => fulfillment.id))
    } else {
      setSelectedFulfillments([])
    }
  }, [fulfillments])

  const handleSelectFulfillment = useCallback((fulfillmentId: string, checked: boolean) => {
    if (checked) {
      setSelectedFulfillments(prev => [...prev, fulfillmentId])
    } else {
      setSelectedFulfillments(prev => prev.filter(id => id !== fulfillmentId))
    }
  }, [])

  // Status badge helper
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { variant: 'secondary' as const, label: 'Pending' },
      processing: { variant: 'default' as const, label: 'Processing' },
      shipped: { variant: 'default' as const, label: 'Shipped' },
      delivered: { variant: 'default' as const, label: 'Delivered' },
      cancelled: { variant: 'destructive' as const, label: 'Cancelled' },
      returned: { variant: 'outline' as const, label: 'Returned' }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || { variant: 'outline' as const, label: status }
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  // Carrier icon helper
  const getCarrierIcon = (carrier: string) => {
    const carrierLower = carrier.toLowerCase()
    if (carrierLower.includes('fedex') || carrierLower.includes('dhl')) {
      return <Plane className="h-4 w-4" />
    }
    if (carrierLower.includes('ship') || carrierLower.includes('ocean')) {
      return <Ship className="h-4 w-4" />
    }
    return <Truck className="h-4 w-4" />
  }

  // Statistics calculations
  const stats = useMemo(() => {
    const totalFulfillments = pagination?.total || 0
    const pendingFulfillments = fulfillments.filter(f => f.status === 'pending').length
    const processingFulfillments = fulfillments.filter(f => f.status === 'processing').length
    const shippedFulfillments = fulfillments.filter(f => f.status === 'shipped').length
    const deliveredFulfillments = fulfillments.filter(f => f.status === 'delivered').length
    
    return {
      total: totalFulfillments,
      pending: pendingFulfillments,
      processing: processingFulfillments,
      shipped: shippedFulfillments,
      delivered: deliveredFulfillments
    }
  }, [fulfillments, pagination])

  return (
    <div className="space-y-6">
      {/* Enhanced Header with Stats */}
      <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Fulfillments</h2>
            <p className="text-muted-foreground">
              Track and manage order shipments
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={handleRefresh} disabled={isRefreshing}>
              <RefreshCw className={`mr-2 h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Export
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem>Export as CSV</DropdownMenuItem>
                <DropdownMenuItem>Export as Excel</DropdownMenuItem>
                <DropdownMenuItem>Shipping Labels</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <Button onClick={onCreateFulfillment}>
              <Plus className="mr-2 h-4 w-4" />
              Create Fulfillment
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Package2 className="h-4 w-4 text-blue-500" />
                <div>
                  <p className="text-sm font-medium">Total</p>
                  <p className="text-2xl font-bold">{stats.total}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-yellow-500" />
                <div>
                  <p className="text-sm font-medium">Pending</p>
                  <p className="text-2xl font-bold">{stats.pending}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Package className="h-4 w-4 text-orange-500" />
                <div>
                  <p className="text-sm font-medium">Processing</p>
                  <p className="text-2xl font-bold">{stats.processing}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Truck className="h-4 w-4 text-blue-500" />
                <div>
                  <p className="text-sm font-medium">Shipped</p>
                  <p className="text-2xl font-bold">{stats.shipped}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <CheckCircle2 className="h-4 w-4 text-green-500" />
                <div>
                  <p className="text-sm font-medium">Delivered</p>
                  <p className="text-2xl font-bold">{stats.delivered}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Filter Presets and Advanced Filters */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center">
              <Filter className="mr-2 h-5 w-5" />
              Filters & Search
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Settings className="mr-2 h-4 w-4" />
                Advanced
              </Button>
              <div className="flex items-center space-x-1">
                <Button
                  variant={viewMode === 'table' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('table')}
                >
                  <List className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Quick Filter Presets */}
          <div className="flex flex-wrap gap-2 mb-4">
            {(['all', 'pending', 'processing', 'shipped', 'delivered', 'cancelled', 'delayed'] as FilterPreset[]).map((preset) => (
              <Button
                key={preset}
                variant={filterPreset === preset ? 'default' : 'outline'}
                size="sm"
                onClick={() => applyFilterPreset(preset)}
              >
                {preset === 'all' && <Package2 className="mr-2 h-3 w-3" />}
                {preset === 'pending' && <Clock className="mr-2 h-3 w-3" />}
                {preset === 'processing' && <Package className="mr-2 h-3 w-3" />}
                {preset === 'shipped' && <Truck className="mr-2 h-3 w-3" />}
                {preset === 'delivered' && <CheckCircle2 className="mr-2 h-3 w-3" />}
                {preset === 'cancelled' && <XCircle className="mr-2 h-3 w-3" />}
                {preset === 'delayed' && <AlertCircle className="mr-2 h-3 w-3" />}
                {preset.charAt(0).toUpperCase() + preset.slice(1)}
              </Button>
            ))}
          </div>

          {/* Search and Basic Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search fulfillments..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select
              value={filters.status.length === 1 ? filters.status[0] : 'all'}
              onValueChange={(value) => {
                if (value === 'all') {
                  setFilters(prev => ({ ...prev, status: [] }))
                } else {
                  setFilters(prev => ({ ...prev, status: [value] }))
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="All statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All statuses</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="processing">Processing</SelectItem>
                <SelectItem value="shipped">Shipped</SelectItem>
                <SelectItem value="delivered">Delivered</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.carrier.length === 1 ? filters.carrier[0] : 'all'}
              onValueChange={(value) => {
                if (value === 'all') {
                  setFilters(prev => ({ ...prev, carrier: [] }))
                } else {
                  setFilters(prev => ({ ...prev, carrier: [value] }))
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="All carriers" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All carriers</SelectItem>
                <SelectItem value="fedex">FedEx</SelectItem>
                <SelectItem value="dhl">DHL</SelectItem>
                <SelectItem value="ups">UPS</SelectItem>
                <SelectItem value="postnet">PostNet</SelectItem>
                <SelectItem value="courier-guy">The Courier Guy</SelectItem>
              </SelectContent>
            </Select>

            <Select value={`${sortField}-${sortDirection}`} onValueChange={(value) => {
              const [field, direction] = value.split('-')
              setSortField(field as 'orderNumber' | 'status' | 'createdAt' | 'estimatedDelivery' | 'carrier')
              setSortDirection(direction as 'asc' | 'desc')
            }}>
              <SelectTrigger>
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="createdAt-desc">
                  <div className="flex items-center">
                    <Calendar className="mr-2 h-4 w-4" />
                    Newest first
                  </div>
                </SelectItem>
                <SelectItem value="createdAt-asc">
                  <div className="flex items-center">
                    <Calendar className="mr-2 h-4 w-4" />
                    Oldest first
                  </div>
                </SelectItem>
                <SelectItem value="estimatedDelivery-asc">
                  <div className="flex items-center">
                    <Clock className="mr-2 h-4 w-4" />
                    Delivery date
                  </div>
                </SelectItem>
                <SelectItem value="orderNumber-asc">
                  <div className="flex items-center">
                    <SortAsc className="mr-2 h-4 w-4" />
                    Order number A-Z
                  </div>
                </SelectItem>
                <SelectItem value="carrier-asc">
                  <div className="flex items-center">
                    <Truck className="mr-2 h-4 w-4" />
                    Carrier A-Z
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Bulk Operations */}
      {selectedFulfillments.length > 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Package2 className="h-4 w-4" />
                <span className="font-medium">
                  {selectedFulfillments.length} fulfillment{selectedFulfillments.length !== 1 ? 's' : ''} selected
                </span>
              </div>
              <Button
                variant="outline"
                onClick={() => setShowBulkOperations(!showBulkOperations)}
              >
                <Zap className="mr-2 h-4 w-4" />
                Bulk Actions
              </Button>
            </div>
            {showBulkOperations && (
              <div className="mt-4 flex flex-wrap gap-2">
                <Button variant="outline" size="sm">Update Status</Button>
                <Button variant="outline" size="sm">Print Labels</Button>
                <Button variant="outline" size="sm">Send Notifications</Button>
                <Button variant="outline" size="sm">Export Selected</Button>
                <Button variant="outline" size="sm">Mark as Shipped</Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button
              variant="outline"
              size="sm"
              className="ml-2"
              onClick={handleRefresh}
            >
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Enhanced Fulfillments Display */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <Package2 className="mr-2 h-5 w-5" />
                Fulfillments
                {selectedFulfillments.length > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {selectedFulfillments.length} selected
                  </Badge>
                )}
              </CardTitle>
              <CardDescription>
                {pagination ? (
                  <div className="flex items-center space-x-4">
                    <span>{pagination.total} total fulfillments</span>
                    <Separator orientation="vertical" className="h-4" />
                    <span>Page {pagination.page} of {pagination.totalPages}</span>
                    <Separator orientation="vertical" className="h-4" />
                    <Select value={pageSize.toString()} onValueChange={(value) => setPageSize(Number(value))}>
                      <SelectTrigger className="w-20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="10">10</SelectItem>
                        <SelectItem value="20">20</SelectItem>
                        <SelectItem value="50">50</SelectItem>
                        <SelectItem value="100">100</SelectItem>
                      </SelectContent>
                    </Select>
                    <span>per page</span>
                  </div>
                ) : 'Loading fulfillments...'}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                <p className="text-sm text-muted-foreground">Loading fulfillments...</p>
              </div>
            </div>
          ) : fulfillments.length === 0 ? (
            <div className="text-center py-12">
              <Package2 className="mx-auto h-16 w-16 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No fulfillments found</h3>
              <p className="text-sm text-muted-foreground mb-6">
                {searchQuery || filters.status.length > 0 || filters.carrier.length > 0
                  ? 'Try adjusting your search or filters to find what you\'re looking for.'
                  : 'No fulfillments have been created yet.'}
              </p>
              <div className="flex items-center justify-center space-x-2">
                {(searchQuery || filters.status.length > 0 || filters.carrier.length > 0) && (
                  <Button variant="outline" onClick={() => {
                    setSearchQuery('')
                    setFilters({
                      status: [],
                      carrier: [],
                      dateRange: null,
                      location: [],
                      delayed: null
                    })
                    setFilterPreset('all')
                  }}>
                    Clear Filters
                  </Button>
                )}
                <Button onClick={onCreateFulfillment}>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Fulfillment
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <Checkbox
                          checked={selectedFulfillments.length === fulfillments.length}
                          onCheckedChange={handleSelectAll}
                        />
                      </TableHead>
                      <TableHead>Order</TableHead>
                      <TableHead>Customer</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Carrier</TableHead>
                      <TableHead>Tracking</TableHead>
                      <TableHead>Delivery</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead className="w-12"></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {fulfillments.map((fulfillment) => (
                      <TableRow key={fulfillment.id} className="group hover:bg-muted/50">
                        <TableCell>
                          <Checkbox
                            checked={selectedFulfillments.includes(fulfillment.id)}
                            onCheckedChange={(checked) =>
                              handleSelectFulfillment(fulfillment.id, checked as boolean)
                            }
                          />
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">#{fulfillment.order.orderNumber}</div>
                            <div className="text-sm text-muted-foreground">
                              {fulfillment.items.length} items
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{fulfillment.order.customerEmail}</div>
                            {(fulfillment.order.customerFirstName || fulfillment.order.customerLastName) && (
                              <div className="text-sm text-muted-foreground">
                                {fulfillment.order.customerFirstName} {fulfillment.order.customerLastName}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>{getStatusBadge(fulfillment.status)}</TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            {getCarrierIcon(fulfillment.carrier)}
                            <span className="text-sm">{fulfillment.carrier}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          {fulfillment.trackingNumber ? (
                            <div>
                              <div className="font-medium text-sm">{fulfillment.trackingNumber}</div>
                              {fulfillment.trackingUrl && (
                                <Button
                                  variant="link"
                                  size="sm"
                                  className="p-0 h-auto text-xs"
                                  onClick={() => onTrackShipment(fulfillment)}
                                >
                                  Track Package
                                </Button>
                              )}
                            </div>
                          ) : (
                            <span className="text-sm text-muted-foreground">No tracking</span>
                          )}
                        </TableCell>
                        <TableCell>
                          {fulfillment.estimatedDelivery ? (
                            <div className="text-sm">
                              {new Date(fulfillment.estimatedDelivery).toLocaleDateString()}
                            </div>
                          ) : (
                            <span className="text-sm text-muted-foreground">TBD</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {new Date(fulfillment.createdAt).toLocaleDateString()}
                          </div>
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => onViewFulfillment(fulfillment)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => onEditFulfillment(fulfillment)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              {fulfillment.trackingNumber && (
                                <DropdownMenuItem onClick={() => onTrackShipment(fulfillment)}>
                                  <MapPin className="mr-2 h-4 w-4" />
                                  Track
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>
                                <Download className="mr-2 h-4 w-4" />
                                Print Label
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
