'use client'

import { useState, useEffect, useMemo, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { 
  Search, 
  Plus, 
  Filter, 
  MoreHorizontal, 
  Edit, 
  Eye,
  Package,
  TrendingUp,
  AlertTriangle,
  Loader2,
  Grid3X3,
  List,
  SortAsc,
  SortDesc,
  Download,
  Settings,
  RefreshCw,
  BarChart3,
  Calendar,
  Tag,
  CheckCircle2,
  XCircle,
  Clock,
  Zap,
  Archive,
  Truck,
  ShoppingCart,
  DollarSign,
  TrendingDown,
  Activity,
  Warehouse,
  AlertCircle
} from 'lucide-react'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { formatCurrency } from '@/lib/utils'

type ViewMode = 'table' | 'grid' | 'compact'
type FilterPreset = 'all' | 'low-stock' | 'out-of-stock' | 'overstocked' | 'reserved' | 'recent-activity'

interface InventoryItem {
  id: string
  productId: string
  productTitle: string
  productSku?: string
  currentStock: number
  reservedStock: number
  availableStock: number
  reorderPoint: number
  maxStock: number
  costPrice: number
  lastUpdated: string
  lastStockMovement?: {
    type: 'in' | 'out' | 'adjustment'
    quantity: number
    reason: string
    date: string
  }
  location?: string
  supplier?: string
  status: 'active' | 'inactive' | 'discontinued'
}

interface EnhancedInventoryListProps {
  onCreateItem: () => void
  onEditItem: (item: InventoryItem) => void
  onViewItem: (item: InventoryItem) => void
  onAdjustStock: (item: InventoryItem) => void
}

interface InventoryFilters {
  status: string[]
  stockLevel: 'all' | 'low-stock' | 'out-of-stock' | 'overstocked'
  location: string[]
  supplier: string[]
  dateRange: { from: Date; to: Date } | null
}

export function EnhancedInventoryList({ 
  onCreateItem, 
  onEditItem, 
  onViewItem,
  onAdjustStock
}: EnhancedInventoryListProps) {
  // View and layout state
  const [viewMode, setViewMode] = useState<ViewMode>('table')
  const [showFilters, setShowFilters] = useState(false)
  const [filterPreset, setFilterPreset] = useState<FilterPreset>('all')
  
  // Search and filtering state
  const [searchQuery, setSearchQuery] = useState('')
  const [debouncedSearch, setDebouncedSearch] = useState('')
  const [filters, setFilters] = useState<InventoryFilters>({
    status: [],
    stockLevel: 'all',
    location: [],
    supplier: [],
    dateRange: null
  })
  
  // Selection and bulk operations
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const [showBulkOperations, setShowBulkOperations] = useState(false)
  
  // Pagination and sorting
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(20)
  const [sortField, setSortField] = useState<'productTitle' | 'currentStock' | 'availableStock' | 'lastUpdated' | 'costPrice'>('lastUpdated')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')
  
  // UI state
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [items, setItems] = useState<InventoryItem[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState<any>(null)

  // Debounce search query
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setDebouncedSearch(searchQuery)
    }, 300)
    return () => clearTimeout(timeoutId)
  }, [searchQuery])

  // Fetch inventory items
  const fetchInventory = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const queryParams = new URLSearchParams()
      if (debouncedSearch) queryParams.set('query', debouncedSearch)
      if (currentPage) queryParams.set('page', currentPage.toString())
      if (pageSize) queryParams.set('limit', pageSize.toString())
      if (sortField) queryParams.set('sortBy', sortField)
      if (sortDirection) queryParams.set('sortOrder', sortDirection)
      
      // Add filters
      if (filters.status.length > 0) queryParams.set('status', filters.status.join(','))
      if (filters.stockLevel !== 'all') queryParams.set('stockLevel', filters.stockLevel)
      if (filters.location.length > 0) queryParams.set('location', filters.location.join(','))
      if (filters.supplier.length > 0) queryParams.set('supplier', filters.supplier.join(','))

      const response = await fetch(`/api/inventory?${queryParams.toString()}`)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const result = await response.json()

      if (result.success && result.data) {
        setItems(result.data.data || [])
        setPagination(result.data.pagination || null)
      } else {
        setError(result.error || 'Failed to fetch inventory')
        setItems([])
        setPagination(null)
      }
    } catch (err) {
      console.error('Error fetching inventory:', err)
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      setItems([])
      setPagination(null)
    } finally {
      setLoading(false)
    }
  }, [debouncedSearch, currentPage, pageSize, sortField, sortDirection, filters])

  // Update search when parameters change
  useEffect(() => {
    fetchInventory()
  }, [fetchInventory])

  // Filter presets
  const applyFilterPreset = useCallback((preset: FilterPreset) => {
    setFilterPreset(preset)
    setCurrentPage(1)
    
    switch (preset) {
      case 'all':
        setFilters({
          status: [],
          stockLevel: 'all',
          location: [],
          supplier: [],
          dateRange: null
        })
        break
      case 'low-stock':
        setFilters(prev => ({ ...prev, stockLevel: 'low-stock' }))
        break
      case 'out-of-stock':
        setFilters(prev => ({ ...prev, stockLevel: 'out-of-stock' }))
        break
      case 'overstocked':
        setFilters(prev => ({ ...prev, stockLevel: 'overstocked' }))
        break
      case 'reserved':
        // Filter items with reserved stock > 0
        break
      case 'recent-activity':
        const weekAgo = new Date()
        weekAgo.setDate(weekAgo.getDate() - 7)
        setFilters(prev => ({ 
          ...prev, 
          dateRange: { from: weekAgo, to: new Date() }
        }))
        break
    }
  }, [])

  // Enhanced refresh with loading state
  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true)
    try {
      await fetchInventory()
    } finally {
      setIsRefreshing(false)
    }
  }, [fetchInventory])

  // Selection handlers
  const handleSelectAll = useCallback((checked: boolean) => {
    if (checked) {
      setSelectedItems(items.map(item => item.id))
    } else {
      setSelectedItems([])
    }
  }, [items])

  const handleSelectItem = useCallback((itemId: string, checked: boolean) => {
    if (checked) {
      setSelectedItems(prev => [...prev, itemId])
    } else {
      setSelectedItems(prev => prev.filter(id => id !== itemId))
    }
  }, [])

  // Stock level badge helper
  const getStockLevelBadge = (item: InventoryItem) => {
    if (item.currentStock === 0) {
      return <Badge variant="destructive">Out of Stock</Badge>
    }
    if (item.currentStock <= item.reorderPoint) {
      return <Badge variant="secondary">Low Stock</Badge>
    }
    if (item.currentStock >= item.maxStock) {
      return <Badge variant="outline">Overstocked</Badge>
    }
    return <Badge variant="default">In Stock</Badge>
  }

  // Status badge helper
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { variant: 'default' as const, label: 'Active' },
      inactive: { variant: 'secondary' as const, label: 'Inactive' },
      discontinued: { variant: 'destructive' as const, label: 'Discontinued' }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || { variant: 'outline' as const, label: status }
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  // Statistics calculations
  const stats = useMemo(() => {
    const totalItems = pagination?.total || 0
    const lowStockItems = items.filter(i => i.currentStock <= i.reorderPoint && i.currentStock > 0).length
    const outOfStockItems = items.filter(i => i.currentStock === 0).length
    const overstockedItems = items.filter(i => i.currentStock >= i.maxStock).length
    const totalValue = items.reduce((sum, i) => sum + (i.currentStock * i.costPrice), 0)
    
    return {
      total: totalItems,
      lowStock: lowStockItems,
      outOfStock: outOfStockItems,
      overstocked: overstockedItems,
      totalValue
    }
  }, [items, pagination])

  return (
    <div className="space-y-6">
      {/* Enhanced Header with Stats */}
      <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Inventory</h2>
            <p className="text-muted-foreground">
              Monitor and manage stock levels
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={handleRefresh} disabled={isRefreshing}>
              <RefreshCw className={`mr-2 h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Export
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem>Export as CSV</DropdownMenuItem>
                <DropdownMenuItem>Export as Excel</DropdownMenuItem>
                <DropdownMenuItem>Stock Report</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <Button onClick={onCreateItem}>
              <Plus className="mr-2 h-4 w-4" />
              Add Item
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Warehouse className="h-4 w-4 text-blue-500" />
                <div>
                  <p className="text-sm font-medium">Total Items</p>
                  <p className="text-2xl font-bold">{stats.total}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-4 w-4 text-yellow-500" />
                <div>
                  <p className="text-sm font-medium">Low Stock</p>
                  <p className="text-2xl font-bold">{stats.lowStock}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <XCircle className="h-4 w-4 text-red-500" />
                <div>
                  <p className="text-sm font-medium">Out of Stock</p>
                  <p className="text-2xl font-bold">{stats.outOfStock}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-4 w-4 text-orange-500" />
                <div>
                  <p className="text-sm font-medium">Overstocked</p>
                  <p className="text-2xl font-bold">{stats.overstocked}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <DollarSign className="h-4 w-4 text-green-500" />
                <div>
                  <p className="text-sm font-medium">Total Value</p>
                  <p className="text-2xl font-bold">{formatCurrency(stats.totalValue, 'ZAR')}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Filter Presets and Advanced Filters */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center">
              <Filter className="mr-2 h-5 w-5" />
              Filters & Search
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Settings className="mr-2 h-4 w-4" />
                Advanced
              </Button>
              <div className="flex items-center space-x-1">
                <Button
                  variant={viewMode === 'table' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('table')}
                >
                  <List className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Quick Filter Presets */}
          <div className="flex flex-wrap gap-2 mb-4">
            {(['all', 'low-stock', 'out-of-stock', 'overstocked', 'reserved', 'recent-activity'] as FilterPreset[]).map((preset) => (
              <Button
                key={preset}
                variant={filterPreset === preset ? 'default' : 'outline'}
                size="sm"
                onClick={() => applyFilterPreset(preset)}
              >
                {preset === 'all' && <Warehouse className="mr-2 h-3 w-3" />}
                {preset === 'low-stock' && <AlertTriangle className="mr-2 h-3 w-3" />}
                {preset === 'out-of-stock' && <XCircle className="mr-2 h-3 w-3" />}
                {preset === 'overstocked' && <TrendingUp className="mr-2 h-3 w-3" />}
                {preset === 'reserved' && <Archive className="mr-2 h-3 w-3" />}
                {preset === 'recent-activity' && <Activity className="mr-2 h-3 w-3" />}
                {preset.charAt(0).toUpperCase() + preset.slice(1).replace('-', ' ')}
              </Button>
            ))}
          </div>

          {/* Search and Basic Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search inventory..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select
              value={filters.status.length === 1 ? filters.status[0] : 'all'}
              onValueChange={(value) => {
                if (value === 'all') {
                  setFilters(prev => ({ ...prev, status: [] }))
                } else {
                  setFilters(prev => ({ ...prev, status: [value] }))
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="All statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All statuses</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="discontinued">Discontinued</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.stockLevel}
              onValueChange={(value) => {
                setFilters(prev => ({ ...prev, stockLevel: value as any }))
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Stock level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All levels</SelectItem>
                <SelectItem value="low-stock">Low Stock</SelectItem>
                <SelectItem value="out-of-stock">Out of Stock</SelectItem>
                <SelectItem value="overstocked">Overstocked</SelectItem>
              </SelectContent>
            </Select>

            <Select value={`${sortField}-${sortDirection}`} onValueChange={(value) => {
              const [field, direction] = value.split('-')
              setSortField(field as 'productTitle' | 'currentStock' | 'availableStock' | 'lastUpdated' | 'costPrice')
              setSortDirection(direction as 'asc' | 'desc')
            }}>
              <SelectTrigger>
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="lastUpdated-desc">
                  <div className="flex items-center">
                    <Calendar className="mr-2 h-4 w-4" />
                    Recently updated
                  </div>
                </SelectItem>
                <SelectItem value="productTitle-asc">
                  <div className="flex items-center">
                    <SortAsc className="mr-2 h-4 w-4" />
                    Product A-Z
                  </div>
                </SelectItem>
                <SelectItem value="currentStock-asc">
                  <div className="flex items-center">
                    <TrendingDown className="mr-2 h-4 w-4" />
                    Lowest stock
                  </div>
                </SelectItem>
                <SelectItem value="currentStock-desc">
                  <div className="flex items-center">
                    <TrendingUp className="mr-2 h-4 w-4" />
                    Highest stock
                  </div>
                </SelectItem>
                <SelectItem value="costPrice-desc">
                  <div className="flex items-center">
                    <DollarSign className="mr-2 h-4 w-4" />
                    Highest value
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Bulk Operations */}
      {selectedItems.length > 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Package className="h-4 w-4" />
                <span className="font-medium">
                  {selectedItems.length} item{selectedItems.length !== 1 ? 's' : ''} selected
                </span>
              </div>
              <Button
                variant="outline"
                onClick={() => setShowBulkOperations(!showBulkOperations)}
              >
                <Zap className="mr-2 h-4 w-4" />
                Bulk Actions
              </Button>
            </div>
            {showBulkOperations && (
              <div className="mt-4 flex flex-wrap gap-2">
                <Button variant="outline" size="sm">Adjust Stock</Button>
                <Button variant="outline" size="sm">Update Prices</Button>
                <Button variant="outline" size="sm">Set Reorder Points</Button>
                <Button variant="outline" size="sm">Export Selected</Button>
                <Button variant="outline" size="sm">Change Status</Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button
              variant="outline"
              size="sm"
              className="ml-2"
              onClick={handleRefresh}
            >
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Enhanced Inventory Display */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <Warehouse className="mr-2 h-5 w-5" />
                Inventory
                {selectedItems.length > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {selectedItems.length} selected
                  </Badge>
                )}
              </CardTitle>
              <CardDescription>
                {pagination ? (
                  <div className="flex items-center space-x-4">
                    <span>{pagination.total} total items</span>
                    <Separator orientation="vertical" className="h-4" />
                    <span>Page {pagination.page} of {pagination.totalPages}</span>
                    <Separator orientation="vertical" className="h-4" />
                    <Select value={pageSize.toString()} onValueChange={(value) => setPageSize(Number(value))}>
                      <SelectTrigger className="w-20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="10">10</SelectItem>
                        <SelectItem value="20">20</SelectItem>
                        <SelectItem value="50">50</SelectItem>
                        <SelectItem value="100">100</SelectItem>
                      </SelectContent>
                    </Select>
                    <span>per page</span>
                  </div>
                ) : 'Loading inventory...'}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                <p className="text-sm text-muted-foreground">Loading inventory...</p>
              </div>
            </div>
          ) : items.length === 0 ? (
            <div className="text-center py-12">
              <Warehouse className="mx-auto h-16 w-16 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No inventory items found</h3>
              <p className="text-sm text-muted-foreground mb-6">
                {searchQuery || filters.status.length > 0 || filters.stockLevel !== 'all'
                  ? 'Try adjusting your search or filters to find what you\'re looking for.'
                  : 'No inventory items have been added yet.'}
              </p>
              <div className="flex items-center justify-center space-x-2">
                {(searchQuery || filters.status.length > 0 || filters.stockLevel !== 'all') && (
                  <Button variant="outline" onClick={() => {
                    setSearchQuery('')
                    setFilters({
                      status: [],
                      stockLevel: 'all',
                      location: [],
                      supplier: [],
                      dateRange: null
                    })
                    setFilterPreset('all')
                  }}>
                    Clear Filters
                  </Button>
                )}
                <Button onClick={onCreateItem}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Item
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <Checkbox
                          checked={selectedItems.length === items.length}
                          onCheckedChange={handleSelectAll}
                        />
                      </TableHead>
                      <TableHead>Product</TableHead>
                      <TableHead>Current Stock</TableHead>
                      <TableHead>Available</TableHead>
                      <TableHead>Reserved</TableHead>
                      <TableHead>Reorder Point</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Value</TableHead>
                      <TableHead>Last Updated</TableHead>
                      <TableHead className="w-12"></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {items.map((item) => (
                      <TableRow key={item.id} className="group hover:bg-muted/50">
                        <TableCell>
                          <Checkbox
                            checked={selectedItems.includes(item.id)}
                            onCheckedChange={(checked) =>
                              handleSelectItem(item.id, checked as boolean)
                            }
                          />
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{item.productTitle}</div>
                            {item.productSku && (
                              <div className="text-sm text-muted-foreground">
                                SKU: {item.productSku}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">{item.currentStock}</span>
                            {getStockLevelBadge(item)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className="font-medium">{item.availableStock}</span>
                        </TableCell>
                        <TableCell>
                          <span className={item.reservedStock > 0 ? 'text-orange-600 font-medium' : ''}>
                            {item.reservedStock}
                          </span>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm">{item.reorderPoint}</span>
                        </TableCell>
                        <TableCell>{getStatusBadge(item.status)}</TableCell>
                        <TableCell>
                          <div className="font-medium">
                            {formatCurrency(item.currentStock * item.costPrice, 'ZAR')}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {new Date(item.lastUpdated).toLocaleDateString()}
                          </div>
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => onViewItem(item)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => onEditItem(item)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => onAdjustStock(item)}>
                                <Package className="mr-2 h-4 w-4" />
                                Adjust Stock
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>
                                <BarChart3 className="mr-2 h-4 w-4" />
                                View History
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
