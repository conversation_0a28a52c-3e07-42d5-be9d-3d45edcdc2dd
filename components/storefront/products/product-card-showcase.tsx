"use client"

import { ProductCard } from "./product-card"

// Sample product data for demonstration
const sampleProducts = [
  {
    id: "1",
    name: "Premium Cotton T-Shirt",
    slug: "premium-cotton-t-shirt",
    description: "Soft and comfortable cotton t-shirt perfect for everyday wear",
    price: 299,
    compareAtPrice: 399,
    images: ["/assets/images/cocomilk_kids-20221116_113539-1613711150.jpg"],
    colors: [{ name: "White", value: "#ffffff" }, { name: "Black", value: "#000000" }],
    sizes: ["XS", "S", "M", "L", "XL"],
    categoryId: "clothing",
    isNew: false,
    isSale: true,
  },
  {
    id: "2", 
    name: "Kids Summer Dress",
    slug: "kids-summer-dress",
    description: "Beautiful summer dress for kids with floral patterns",
    price: 450,
    images: ["/assets/images/cocomilk_kids-20221116_113539-1613711150.jpg"],
    colors: [{ name: "<PERSON>", value: "#ffc0cb" }, { name: "Blue", value: "#87ceeb" }],
    sizes: ["2T", "3T", "4T", "5T"],
    categoryId: "kids",
    isNew: true,
    isSale: false,
  },
  {
    id: "3",
    name: "School Uniform Shirt",
    slug: "school-uniform-shirt",
    description: "High-quality school uniform shirt with durable fabric",
    price: 199,
    compareAtPrice: 249,
    images: ["/assets/images/cocomilk_kids-20221116_113539-1613711150.jpg"],
    colors: [{ name: "White", value: "#ffffff" }, { name: "Light Blue", value: "#add8e6" }],
    sizes: ["6", "8", "10", "12", "14"],
    categoryId: "uniforms",
    isNew: false,
    isSale: true,
  },
  {
    id: "4",
    name: "Casual Jeans",
    slug: "casual-jeans",
    description: "Comfortable casual jeans for everyday wear",
    price: 599,
    images: [], // No image to test placeholder
    colors: [{ name: "Blue", value: "#4169e1" }, { name: "Black", value: "#000000" }],
    sizes: ["28", "30", "32", "34", "36"],
    categoryId: "clothing",
    isNew: false,
    isSale: false,
  }
]

export function ProductCardShowcase() {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold text-center mb-8">Enhanced Product Card Showcase</h1>
      
      {/* Default Variant */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-6">Default Variant</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {sampleProducts.map((product) => (
            <ProductCard
              key={product.id}
              product={product}
              variant="default"
              showQuickActions={true}
              showRating={true}
              showCompare={true}
            />
          ))}
        </div>
      </section>

      {/* Compact Variant */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-6">Compact Variant</h2>
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4">
          {sampleProducts.map((product) => (
            <ProductCard
              key={`compact-${product.id}`}
              product={product}
              variant="compact"
              showQuickActions={true}
              showRating={false}
              showCompare={false}
            />
          ))}
        </div>
      </section>

      {/* Featured Variant */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-6">Featured Variant</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {sampleProducts.slice(0, 3).map((product) => (
            <ProductCard
              key={`featured-${product.id}`}
              product={product}
              variant="featured"
              showQuickActions={true}
              showRating={true}
              showCompare={true}
              priority={true}
            />
          ))}
        </div>
      </section>

      {/* Minimal Variant (no quick actions) */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-6">Minimal Variant (No Quick Actions)</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {sampleProducts.map((product) => (
            <ProductCard
              key={`minimal-${product.id}`}
              product={product}
              variant="default"
              showQuickActions={false}
              showRating={true}
              showCompare={false}
            />
          ))}
        </div>
      </section>

      {/* Features List */}
      <section className="mt-16 bg-gray-50 rounded-lg p-8">
        <h2 className="text-2xl font-semibold mb-6">Enhanced Features</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <h3 className="font-semibold text-lg mb-2">🖼️ Smart Image Handling</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Automatic placeholder fallback</li>
              <li>• Loading states with spinner</li>
              <li>• Error handling for broken images</li>
              <li>• Smooth hover zoom effects</li>
            </ul>
          </div>
          
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <h3 className="font-semibold text-lg mb-2">⚡ Interactive Elements</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Quick add to cart button</li>
              <li>• Wishlist toggle with heart icon</li>
              <li>• Product comparison feature</li>
              <li>• Quick view functionality</li>
            </ul>
          </div>
          
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <h3 className="font-semibold text-lg mb-2">🎨 Visual Enhancements</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Enhanced badges with icons</li>
              <li>• Star ratings display</li>
              <li>• Stock status indicators</li>
              <li>• Smooth hover animations</li>
            </ul>
          </div>
          
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <h3 className="font-semibold text-lg mb-2">📱 Responsive Design</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Multiple card variants</li>
              <li>• Mobile-optimized layouts</li>
              <li>• Touch-friendly interactions</li>
              <li>• Adaptive image ratios</li>
            </ul>
          </div>
          
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <h3 className="font-semibold text-lg mb-2">🔔 User Feedback</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Toast notifications</li>
              <li>• Loading states</li>
              <li>• Error handling</li>
              <li>• Success confirmations</li>
            </ul>
          </div>
          
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <h3 className="font-semibold text-lg mb-2">⚙️ Customizable</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Toggle quick actions</li>
              <li>• Show/hide ratings</li>
              <li>• Enable/disable compare</li>
              <li>• Multiple variants</li>
            </ul>
          </div>
        </div>
      </section>
    </div>
  )
}