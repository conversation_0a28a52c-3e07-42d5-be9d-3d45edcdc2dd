"use client"

import type React from "react"

import Image from "next/image"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Eye, Scale } from "lucide-react"
import { QuickView } from "@/components/quick-view"
import { usePriceFormatter } from "@/hooks/use-price-formatter"
import { cn } from "@/lib/utils"
import { Product as EcommerceProduct } from "@/lib/ecommerce/types"

// Support both legacy and new product formats
interface LegacyProduct {
  id: string
  name: string
  slug: string
  description: string
  price: number
  compareAtPrice?: number
  images: string[]
  colors: { name: string; value: string }[]
  sizes: string[]
  categoryId: string
  isNew?: boolean
  isSale?: boolean
}

interface ProductCardProps {
  product: EcommerceProduct | LegacyProduct
  className?: string
}

// Type guard to check if product is legacy format
function isLegacyProduct(product: EcommerceProduct | LegacyProduct): product is LegacyProduct {
  return 'name' in product && typeof (product as any).price === 'number'
}

export function ProductCard({ product, className }: ProductCardProps) {
  const { formatPrice } = usePriceFormatter()

  // Handle both legacy and new product formats
  let price: number
  let compareAtPrice: number | undefined
  let imageUrl: string
  let title: string
  let isOnSale: boolean
  let isNew: boolean

  if (isLegacyProduct(product)) {
    // Legacy product format
    price = product.price
    compareAtPrice = product.compareAtPrice
    imageUrl = product.images?.[0] || "/assets/images/cocomilk_kids-20221116_113539-1613711150.jpg"
    title = product.name
    isOnSale = product.isSale || (!!compareAtPrice && compareAtPrice > price)
    isNew = product.isNew || false
  } else {
    // New e-commerce product format
    price = typeof product.price === 'object' ? product.price.amount : product.price
    compareAtPrice = product.compareAtPrice ?
      (typeof product.compareAtPrice === 'object' ? product.compareAtPrice.amount : product.compareAtPrice) :
      undefined
    imageUrl = product.images && product.images.length > 0
      ? product.images[0].url
      : "/assets/images/cocomilk_kids-20221116_113539-1613711150.jpg"
    title = product.title
    isOnSale = !!compareAtPrice && compareAtPrice > price
    isNew = product.createdAt && new Date(product.createdAt) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 30 days
  }

  const discount = compareAtPrice
    ? Math.round(((compareAtPrice - price) / compareAtPrice) * 100)
    : 0

  const handleAddToCompare = (e: React.MouseEvent) => {
    e.preventDefault()
    if (typeof window !== "undefined" && (window as any).addToCompare) {
      ;(window as any).addToCompare(product.id)
    }
  }

  return (
    <div className={cn("group block product-card-clean", className)}>
      {/* Clean image container like selfi.co.za */}
      <div className="relative aspect-[3/4] mb-3 overflow-hidden bg-white image-zoom">
        <Link href={`/products/${product.slug}`}>
          <Image
            src={imageUrl}
            alt={title}
            fill
            className="object-cover"
          />
        </Link>

        {/* Simple badges like selfi.co.za */}
        {isOnSale && (
          <div className="absolute top-2 left-2">
            <span className="text-xs text-red-600 font-normal">
              Sale
            </span>
          </div>
        )}

        {isNew && !isOnSale && (
          <div className="absolute top-2 left-2">
            <span className="text-xs text-black font-normal">
              New
            </span>
          </div>
        )}
      </div>

      {/* Clean product info like selfi.co.za */}
      <div className="space-y-1">
        <Link href={`/products/${product.slug}`}>
          <h3 className="product-title text-black hover:text-gray-600 transition-colors duration-200">
            {title}
          </h3>
        </Link>

        {/* Vendor info */}
        <p className="text-xs text-gray-500 font-normal">
          {isLegacyProduct(product) ? 'Coco Milk Kids' : (product.vendor || 'Coco Milk Kids')}
        </p>

        {/* Price */}
        <div className="flex items-center space-x-2">
          {compareAtPrice ? (
            <>
              <span className="text-sm font-normal text-black">
                {formatPrice(price)}
              </span>
              <span className="text-sm text-gray-500 line-through">
                {formatPrice(compareAtPrice)}
              </span>
            </>
          ) : (
            <span className="text-sm font-normal text-black">
              {formatPrice(price)}
            </span>
          )}
        </div>
      </div>
    </div>
  )
}
