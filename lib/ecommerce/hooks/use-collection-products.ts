// Hook for fetching products by collection type
'use client'

import { useState, useEffect, useCallback } from 'react'
import { Product, PaginatedResponse, ApiResponse } from '../types'
import { buildCollectionSearchParams } from '../utils/product-transformers'

export interface UseCollectionProductsOptions {
  collectionType: string
  sortBy?: string
  filters?: any
  page?: number
  limit?: number
  autoFetch?: boolean
}

export interface UseCollectionProductsReturn {
  products: Product[]
  loading: boolean
  error: { code: string; message: string } | null
  pagination: PaginatedResponse<Product>['pagination'] | null
  refetch: () => Promise<void>
  clearError: () => void
}

/**
 * Hook for fetching products by collection type (best-sellers, new-arrivals, sale, etc.)
 */
export function useCollectionProducts(options: UseCollectionProductsOptions): UseCollectionProductsReturn {
  const { 
    collectionType, 
    sortBy = 'featured', 
    filters = {}, 
    page = 1, 
    limit = 20, 
    autoFetch = true 
  } = options

  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<{ code: string; message: string } | null>(null)
  const [pagination, setPagination] = useState<PaginatedResponse<Product>['pagination'] | null>(null)

  const fetchProducts = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const searchParams = buildCollectionSearchParams(collectionType, sortBy, filters, page, limit)
      
      // Build query parameters
      const params = new URLSearchParams({
        page: searchParams.page.toString(),
        limit: searchParams.limit.toString(),
        sortBy: searchParams.sort.field,
        sortDirection: searchParams.sort.direction
      })

      // Add filters
      if (searchParams.filters.status) {
        searchParams.filters.status.forEach((status: string) => {
          params.append('status', status)
        })
      }

      if (searchParams.filters.featured) {
        params.append('featured', 'true')
      }

      if (searchParams.filters.onSale) {
        params.append('onSale', 'true')
      }

      if (searchParams.filters.createdAfter) {
        params.append('createdAfter', searchParams.filters.createdAfter)
      }

      if (searchParams.filters.tags && searchParams.filters.tags.length > 0) {
        searchParams.filters.tags.forEach((tag: string) => {
          params.append('tags', tag)
        })
      }

      if (searchParams.filters.categoryIds && searchParams.filters.categoryIds.length > 0) {
        searchParams.filters.categoryIds.forEach((categoryId: string) => {
          params.append('categoryIds', categoryId)
        })
      }

      const response = await fetch(`/api/e-commerce/products?${params}`)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result: ApiResponse<PaginatedResponse<Product>> = await response.json()

      if (result.success && result.data) {
        setProducts(result.data.data || [])
        setPagination(result.data.pagination || null)
      } else {
        setError(result.error || { code: 'FETCH_ERROR', message: 'Failed to fetch products' })
        setProducts([])
        setPagination(null)
      }
    } catch (err) {
      console.error('Error fetching collection products:', err)
      setError({
        code: 'NETWORK_ERROR',
        message: err instanceof Error ? err.message : 'An unexpected error occurred'
      })
      setProducts([])
      setPagination(null)
    } finally {
      setLoading(false)
    }
  }, [collectionType, sortBy, filters, page, limit])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  useEffect(() => {
    if (autoFetch) {
      fetchProducts()
    }
  }, [autoFetch, fetchProducts])

  return {
    products,
    loading,
    error,
    pagination,
    refetch: fetchProducts,
    clearError
  }
}

/**
 * Hook for fetching best-selling products
 */
export function useBestSellers(options: Omit<UseCollectionProductsOptions, 'collectionType'> = {}) {
  return useCollectionProducts({
    ...options,
    collectionType: 'best-sellers'
  })
}

/**
 * Hook for fetching new arrival products
 */
export function useNewArrivals(options: Omit<UseCollectionProductsOptions, 'collectionType'> = {}) {
  return useCollectionProducts({
    ...options,
    collectionType: 'new-arrivals'
  })
}

/**
 * Hook for fetching sale products
 */
export function useSaleProducts(options: Omit<UseCollectionProductsOptions, 'collectionType'> = {}) {
  return useCollectionProducts({
    ...options,
    collectionType: 'sale'
  })
}

/**
 * Hook for fetching heritage day products
 */
export function useHeritageProducts(options: Omit<UseCollectionProductsOptions, 'collectionType'> = {}) {
  return useCollectionProducts({
    ...options,
    collectionType: 'heritage-day'
  })
}

/**
 * Hook for fetching school uniform products
 */
export function useSchoolUniforms(options: Omit<UseCollectionProductsOptions, 'collectionType'> = {}) {
  return useCollectionProducts({
    ...options,
    collectionType: 'school-uniforms'
  })
}

/**
 * Hook for fetching summer collection products
 */
export function useSummerProducts(options: Omit<UseCollectionProductsOptions, 'collectionType'> = {}) {
  return useCollectionProducts({
    ...options,
    collectionType: 'summer'
  })
}
